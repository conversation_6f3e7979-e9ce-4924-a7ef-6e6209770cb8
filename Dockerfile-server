# 第一阶段：构建Python依赖
FROM python:3.10-slim AS builder

# 配置Debian镜像源
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list

# 配置PyPI镜像源
RUN pip config set global.index-url https://mirrors.hust.edu.cn/pypi/web/simple/ && \
    pip config set global.trusted-host mirrors.hust.edu.cn

WORKDIR /app

COPY main/xiaozhi-server/requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 第二阶段：生产镜像
FROM python:3.10-slim

# 配置Debian镜像源
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list

WORKDIR /opt/xiaozhi-esp32-server

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends libopus0 ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 从构建阶段复制Python包和前端构建产物
COPY --from=builder /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages
COPY --from=builder /usr/local/bin/mcp-proxy /usr/local/bin/mcp-proxy

# 复制应用代码
COPY main/xiaozhi-server .

# 启动应用
CMD ["python", "app.py"]