#!/bin/bash

# 脚本功能：打包本地项目，上传到服务器，并执行远程构建脚本。

# --- 配置 ---
# 设置服务器的用户和主机
SERVER_USER_HOST="root@**************"
# 设置服务器上的目标目录
REMOTE_DIR="/root/xiaozhi"
# ---

# 启用错误退出：任何命令失败，脚本将立即退出
set -e

# 1. 打包
# 创建一个唯一的压缩包文件名
ARCHIVE_NAME="xiaozhi-server-$(date +%Y%m%d-%H%M%S).tar.gz"
echo "==> 正在打包项目为 ${ARCHIVE_NAME}..."

# 定义要排除的文件和目录列表
EXCLUDE_LIST=(
  --exclude=".git/"
  --exclude="__pycache__/"
  --exclude=".idea/"
  --exclude="*.py[cod]"
  --exclude="*\$py.class"
  --exclude="*.so"
  --exclude=".Python"
  --exclude="build/"
  --exclude="develop-eggs/"
  --exclude="dist/"
  --exclude="downloads/"
  --exclude="eggs/"
  --exclude=".eggs/"
  --exclude="lib/"
  --exclude="lib64/"
  --exclude="parts/"
  --exclude="sdist/"
  --exclude="var/"
  --exclude="wheels/"
  --exclude="share/python-wheels/"
  --exclude="*.egg-info/"
  --exclude=".installed.cfg"
  --exclude="*.egg"
  --exclude="MANIFEST"
  --exclude="*.manifest"
  --exclude="*.spec"
  --exclude="pip-log.txt"
  --exclude="pip-delete-this-directory.txt"
  --exclude="htmlcov/"
  --exclude=".tox/"
  --exclude=".nox/"
  --exclude=".coverage"
  --exclude=".coverage.*"
  --exclude=".cache"
  --exclude="nosetests.xml"
  --exclude="coverage.xml"
  --exclude="*.cover"
  --exclude="*.py,cover"
  --exclude=".hypothesis/"
  --exclude=".pytest_cache/"
  --exclude="cover/"
  --exclude="*.mo"
  --exclude="*.pot"
  --exclude="*.log"
  --exclude="local_settings.py"
  --exclude="db.sqlite3"
  --exclude="db.sqlite3-journal"
  --exclude="instance/"
  --exclude=".webassets-cache"
  --exclude=".scrapy"
  --exclude="docs/_build/"
  --exclude=".pybuilder/"
  --exclude="target/"
  --exclude="*.pid"
  --exclude=".ipynb_checkpoints"
  --exclude="profile_default/"
  --exclude="ipython_config.py"
  --exclude="__pypackages__/"
  --exclude="celerybeat-schedule"
  --exclude="celerybeat.pid"
  --exclude="*.sage.py"
  --exclude=".env"
  --exclude=".venv"
  --exclude="env/"
  --exclude="venv/"
  --exclude="ENV/"
  --exclude="env.bak/"
  --exclude="venv.bak/"
  --exclude=".spyderproject"
  --exclude=".spyproject"
  --exclude=".ropeproject"
  --exclude="/site"
  --exclude=".mypy_cache/"
  --exclude=".dmypy.json"
  --exclude="dmypy.json"
  --exclude=".pyre/"
  --exclude="music/"
  --exclude=".pytype/"
  --exclude="cython_debug/"
  --exclude="*.iml"
  --exclude="tmp"
  --exclude=".history"
  --exclude=".DS_Store"
  --exclude="main/xiaozhi-server/data"
  --exclude="main/manager-web/node_modules"
  --exclude=".config.yaml"
  --exclude=".secrets.yaml"
  --exclude=".private_config.yaml"
  --exclude=".env.development"
  --exclude="main/xiaozhi-server/models/SenseVoiceSmall/model.pt"
  --exclude="main/xiaozhi-server/models/sherpa-onnx*"
  --exclude="/main/xiaozhi-server/audio_ref/"
  --exclude="/audio_ref/"
  --exclude="/asr-models/iic/SenseVoiceSmall/"
  --exclude="/main/xiaozhi-server/asr-models/iic/SenseVoiceSmall/"
  --exclude="/models/SenseVoiceSmall/model.pt"
  --exclude="my_wakeup_words.mp3"
  --exclude="main/manager-api/.vscode"
  --exclude="main/manager-web/.webpack_cache/"
  --exclude="main/xiaozhi-server/mysql"
  --exclude="uploadfile"
  --exclude=".vscode"
  --exclude=".cursor"
  --exclude="docker-publish.config"
  --exclude=".fastRequest/"
  --exclude=".trace/"
  --exclude=".trae/"
  --exclude=".docker_cache/"
  --exclude=".roo/"
  --exclude="dev-tools/"
  --exclude=".github/"
  --exclude=".roomodes"
  --exclude="memory-bank/"
  --exclude="analysis-docs/"
  --exclude="docker-cache-optimization/*"
  --exclude="uv.lock"
  --exclude=".webpack_cache/"
  # 额外建议
  --exclude="docs/*.md"
  --exclude="docs/images/"
  --exclude="test/"
  --exclude="specs/"
  --exclude="test-scripts/"
  --exclude="*.md"
  # 同时排除脚本自身和压缩包
  --exclude="*.tar.gz"
  --exclude="server_build.sh"
  --exclude="deploy_to_server.sh"
)

# 使用 tar 命令打包，并应用排除列表
# 设置环境变量以避免 macOS 扩展属性警告
export COPYFILE_DISABLE=1
export TAR_OPTIONS="--no-xattrs --no-mac-metadata"
tar "${EXCLUDE_LIST[@]}" --no-xattrs --no-mac-metadata -zcf "${ARCHIVE_NAME}" .

# 检查打包是否成功
if [ $? -ne 0 ]; then
  echo "!!! 打包失败！"
  exit 1
fi
echo "==> 打包成功。"

# 2. 上传
echo "==> 正在上传 ${ARCHIVE_NAME} 到 ${SERVER_USER_HOST}:${REMOTE_DIR}..."
scp "${ARCHIVE_NAME}" "${SERVER_USER_HOST}:${REMOTE_DIR}"

# 检查上传是否成功
if [ $? -ne 0 ]; then
  echo "!!! 上传失败！"
  # 清理本地压缩包
  rm -f "${ARCHIVE_NAME}"
  exit 1
fi
echo "==> 上传成功。"

# 3. 远程执行
echo "==> 正在远程执行构建脚本..."
ssh "${SERVER_USER_HOST}" "cd ${REMOTE_DIR} && ./server_build.sh ${ARCHIVE_NAME}"

# 检查远程执行是否成功
if [ $? -ne 0 ]; then
  echo "!!! 远程构建失败！"
  # 清理本地压缩包
  rm -f "${ARCHIVE_NAME}"
  exit 1
fi
echo "==> 远程构建成功。"

# 4. 清理
echo "==> 正在清理本地压缩包..."
rm -f "${ARCHIVE_NAME}"
echo "==> 清理完成。"

echo "--- 部署成功！ ---"