#!/usr/bin/env python3
"""
小智ESP32服务器 Docker 构建工具 (Python版)
直接复用项目现有配置，简化Docker构建和推送流程
"""
import docker
import os
import sys
import json
import time
from pathlib import Path

class XiaozhiDockerBuilder:
    def __init__(self):
        """初始化构建器，加载环境配置"""
        self.load_env_config()
        self.setup_docker_configs()
    
    def load_env_config(self):
        """加载.env环境配置"""
        env_file = Path('.env')
        if not env_file.exists():
            print("❌ 错误: .env文件不存在")
            print("💡 请复制.env.template为.env并填写配置")
            sys.exit(1)
        
        # 简单的.env文件解析
        self.env_vars = {}
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    self.env_vars[key.strip()] = value.strip()
    
    def setup_docker_configs(self):
        """设置Docker服务器配置"""
        self.docker_configs = {
            'test': {
                'host': f"https://{self.env_vars.get('TEST_HOST')}:{self.env_vars.get('TEST_PORT', '2376')}",
                'cert_path': self.env_vars.get('TEST_CERT_PATH'),
                'name': '测试环境'
            },
            'prod': {
                'host': f"https://{self.env_vars.get('PROD_HOST')}:{self.env_vars.get('PROD_PORT', '2376')}",
                'cert_path': self.env_vars.get('PROD_CERT_PATH'),
                'name': '生产环境'
            }
        }
        
        # 验证配置
        for env_name, config in self.docker_configs.items():
            if not config['cert_path'] or not os.path.exists(config['cert_path']):
                print(f"⚠️  警告: {config['name']}证书路径不存在: {config['cert_path']}")
    
    def get_docker_client(self, env='test'):
        """获取Docker客户端"""
        config = self.docker_configs[env]
        cert_path = config['cert_path']
        
        # 检查证书文件
        cert_files = ['ca.pem', 'cert.pem', 'key.pem']
        for cert_file in cert_files:
            full_path = os.path.join(cert_path, cert_file)
            if not os.path.exists(full_path):
                raise FileNotFoundError(f"证书文件不存在: {full_path}")
        
        # 创建TLS配置
        tls_config = docker.tls.TLSConfig(
            client_cert=(
                os.path.join(cert_path, 'cert.pem'),
                os.path.join(cert_path, 'key.pem')
            ),
            ca_cert=os.path.join(cert_path, 'ca.pem'),
            verify=True
        )
        
        return docker.DockerClient(
            base_url=config['host'],
            tls=tls_config
        )
    
    def build_image(self, env='test', image_type='server', tag='latest', dockerfile=None, context='.'):
        """构建Docker镜像"""
        config = self.docker_configs[env]
        
        # 确定Dockerfile
        if not dockerfile:
            dockerfile = f'Dockerfile-{image_type}'
        
        # 构建镜像名称
        registry = self.env_vars.get('REMOTE_REGISTRY', 'xiaozhi-esp32-server')
        image_name = f"{registry}:{image_type}_{tag}"
        
        print(f"🚀 开始构建镜像")
        print(f"   📁 构建上下文: {context}")
        print(f"   📄 Dockerfile: {dockerfile}")
        print(f"   🏷️  镜像名称: {image_name}")
        print(f"   🌍 目标环境: {config['name']} ({env})")
        print("-" * 60)
        
        try:
            client = self.get_docker_client(env)
            
            # 构建镜像
            image, logs = client.images.build(
                path=context,
                dockerfile=dockerfile,
                tag=image_name,
                rm=True,
                pull=True,
                buildargs={
                    'PIP_INDEX_URL': self.env_vars.get('PIP_MIRROR', 'https://pypi.org/simple/'),
                    'NPM_MIRROR': self.env_vars.get('NPM_MIRROR', 'https://registry.npmjs.org/'),
                    'MAVEN_MIRROR': self.env_vars.get('MAVEN_MIRROR', 'https://repo1.maven.org/maven2'),
                    'DEBIAN_MIRROR': self.env_vars.get('DEBIAN_MIRROR', 'http://deb.debian.org/debian/'),
                }
            )
            
            # 显示构建日志
            for log in logs:
                if 'stream' in log:
                    print(f"   {log['stream'].strip()}")
                elif 'error' in log:
                    print(f"   ❌ {log['error']}")
                    return False
            
            print(f"✅ 镜像构建成功!")
            print(f"   🆔 镜像ID: {image.short_id}")
            print(f"   🏷️  镜像标签: {image_name}")
            return image_name
            
        except Exception as e:
            print(f"❌ 构建失败: {e}")
            return False
    
    def push_image(self, env='test', image_name=None):
        """推送镜像到远程仓库"""
        if not image_name:
            print("❌ 错误: 未指定镜像名称")
            return False
        
        config = self.docker_configs[env]
        print(f"📤 开始推送镜像")
        print(f"   🏷️  镜像名称: {image_name}")
        print(f"   🌍 目标环境: {config['name']} ({env})")
        print("-" * 60)
        
        try:
            client = self.get_docker_client(env)
            
            # 推送镜像
            for line in client.images.push(image_name, stream=True, decode=True):
                if 'status' in line:
                    status = line['status']
                    if 'progress' in line:
                        print(f"   📊 {status}: {line['progress']}")
                    else:
                        print(f"   📊 {status}")
                elif 'error' in line:
                    print(f"   ❌ {line['error']}")
                    return False
            
            print(f"✅ 镜像推送成功: {image_name}")
            return True
            
        except Exception as e:
            print(f"❌ 推送失败: {e}")
            return False
    
    def build_and_push(self, env='test', image_type='server', tag='latest'):
        """构建并推送镜像（一步完成）"""
        print(f"🔄 开始构建并推送流程")
        print(f"   📦 镜像类型: {image_type}")
        print(f"   🏷️  标签: {tag}")
        print(f"   🌍 环境: {self.docker_configs[env]['name']}")
        print("=" * 60)
        
        # 构建镜像
        image_name = self.build_image(env, image_type, tag)
        if not image_name:
            return False
        
        print("\n" + "=" * 60)
        
        # 推送镜像
        return self.push_image(env, image_name)
    
    def list_images(self, env='test'):
        """列出远程环境的镜像"""
        config = self.docker_configs[env]
        print(f"📋 {config['name']}镜像列表:")
        print("-" * 80)
        
        try:
            client = self.get_docker_client(env)
            images = client.images.list()
            
            if not images:
                print("   📭 没有找到镜像")
                return
            
            for image in images:
                tags = ', '.join(image.tags) if image.tags else '<none>'
                size = self.format_size(image.attrs.get('Size', 0))
                created = image.attrs.get('Created', '')[:19].replace('T', ' ')
                print(f"🆔 {image.short_id} | 🏷️  {tags}")
                print(f"   📏 大小: {size} | 📅 创建: {created}")
                print("-" * 80)
                
        except Exception as e:
            print(f"❌ 获取镜像列表失败: {e}")
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f}{size_names[i]}"

def main():
    """命令行入口"""
    if len(sys.argv) < 2:
        print("🔧 小智ESP32服务器 Docker构建工具")
        print("=" * 50)
        print("使用方法:")
        print("  python docker_builder.py build <env> [image_type] [tag]")
        print("  python docker_builder.py push <env> <image_name>")
        print("  python docker_builder.py deploy <env> [image_type] [tag]")
        print("  python docker_builder.py list <env>")
        print("")
        print("参数说明:")
        print("  env        : test | prod")
        print("  image_type : server | web")
        print("  tag        : 镜像标签 (默认: latest)")
        print("")
        print("示例:")
        print("  python docker_builder.py build test server v1.0")
        print("  python docker_builder.py deploy prod web latest")
        print("  python docker_builder.py list test")
        return
    
    builder = XiaozhiDockerBuilder()
    action = sys.argv[1]
    
    if action == 'build':
        if len(sys.argv) < 3:
            print("❌ 错误: 请指定环境 (test/prod)")
            return
        env = sys.argv[2]
        image_type = sys.argv[3] if len(sys.argv) > 3 else 'server'
        tag = sys.argv[4] if len(sys.argv) > 4 else 'latest'
        builder.build_image(env, image_type, tag)
        
    elif action == 'push':
        if len(sys.argv) < 4:
            print("❌ 错误: 请指定环境和镜像名称")
            return
        env = sys.argv[2]
        image_name = sys.argv[3]
        builder.push_image(env, image_name)
        
    elif action == 'deploy':
        if len(sys.argv) < 3:
            print("❌ 错误: 请指定环境 (test/prod)")
            return
        env = sys.argv[2]
        image_type = sys.argv[3] if len(sys.argv) > 3 else 'server'
        tag = sys.argv[4] if len(sys.argv) > 4 else 'latest'
        builder.build_and_push(env, image_type, tag)
        
    elif action == 'list':
        if len(sys.argv) < 3:
            print("❌ 错误: 请指定环境 (test/prod)")
            return
        env = sys.argv[2]
        builder.list_images(env)
        
    else:
        print(f"❌ 未知操作: {action}")

if __name__ == '__main__':
    main()
