package xiaozhi.modules.agent.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.PageData;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.common.utils.JsonUtils;
import xiaozhi.modules.agent.dao.AgentDao;
import xiaozhi.modules.agent.dto.AgentCreateDTO;
import xiaozhi.modules.agent.dto.AgentDTO;
import xiaozhi.modules.agent.dto.AgentMappingDTO;
import xiaozhi.modules.agent.dto.AgentUpdateDTO;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.agent.entity.AgentPluginMapping;
import xiaozhi.modules.agent.entity.AgentTemplateEntity;
import xiaozhi.modules.agent.service.AgentChatHistoryService;
import xiaozhi.modules.agent.service.AgentPluginMappingService;
import xiaozhi.modules.agent.service.AgentService;
import xiaozhi.modules.agent.service.AgentTemplateService;
import xiaozhi.modules.agent.vo.AgentInfoVO;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.model.dto.ModelProviderDTO;
import xiaozhi.modules.model.service.ModelConfigService;
import xiaozhi.modules.model.service.ModelProviderService;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.sys.enums.SuperAdminEnum;
import xiaozhi.modules.timbre.service.TimbreService;

@Slf4j
@Service
@AllArgsConstructor
public class AgentServiceImpl extends BaseServiceImpl<AgentDao, AgentEntity> implements AgentService {
    private final AgentDao agentDao;
    private final TimbreService timbreModelService;
    private final ModelConfigService modelConfigService;
    private final RedisUtils redisUtils;
    private final DeviceService deviceService;
    private final AgentPluginMappingService agentPluginMappingService;
    private final AgentChatHistoryService agentChatHistoryService;
    private final AgentTemplateService agentTemplateService;
    private final ModelProviderService modelProviderService;

    @Override
    public PageData<AgentEntity> adminAgentList(Map<String, Object> params) {
        IPage<AgentEntity> page = agentDao.selectPage(
                getPage(params, "agent_name", true),
                new QueryWrapper<>());
        return new PageData<>(page.getRecords(), page.getTotal());
    }

    @Override
    public AgentInfoVO getAgentById(String id) {
        AgentInfoVO agent = agentDao.selectAgentInfoById(id);

        if (agent == null) {
            throw new RenException("智能体不存在");
        }

        if (agent.getMemModelId() != null && agent.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            agent.setChatHistoryConf(Constant.ChatHistoryConfEnum.IGNORE.getCode());
            if (agent.getChatHistoryConf() == null) {
                agent.setChatHistoryConf(Constant.ChatHistoryConfEnum.RECORD_TEXT_AUDIO.getCode());
            }
        }
        // 无需额外查询插件列表，已通过SQL查询出来
        return agent;
    }

    @Override
    public boolean insert(AgentEntity entity) {
        // 如果ID为空，自动生成一个UUID作为ID
        if (entity.getId() == null || entity.getId().trim().isEmpty()) {
            entity.setId(UUID.randomUUID().toString().replace("-", ""));
        }

        // 如果智能体编码为空，自动生成一个带前缀的编码
        if (entity.getAgentCode() == null || entity.getAgentCode().trim().isEmpty()) {
            entity.setAgentCode("AGT_" + System.currentTimeMillis());
        }

        // 如果排序字段为空，设置默认值0
        if (entity.getSort() == null) {
            entity.setSort(0);
        }

        // 普通用户创建的智能体强制设置为"user"类型，忽略用户传入的参数
        // 只有超级管理员才能创建默认智能体
        UserDetail currentUser = SecurityUser.getUser();
        boolean isSuperAdmin = currentUser != null && currentUser.getSuperAdmin() == SuperAdminEnum.YES.value();

        if (!isSuperAdmin) {
            // 普通用户强制设置为user类型
            if ("default".equals(entity.getAgentType())) {
                log.debug("普通用户尝试创建默认智能体，已强制设置为用户智能体。用户ID: {}",
                        currentUser != null ? currentUser.getId() : "未知");
            }
            entity.setAgentType("user");
        } else {
            // 超级管理员可以创建任意类型，如果未指定则默认为user
            if (entity.getAgentType() == null) {
                entity.setAgentType("user");
            }
        }
        if (entity.getIsActive() == null) {
            entity.setIsActive(true);
        }

        return super.insert(entity);
    }

    @Override
    public void deleteAgentByUserId(Long userId) {
        UpdateWrapper<AgentEntity> wrapper = new UpdateWrapper<>();
        wrapper.eq("user_id", userId);
        baseDao.delete(wrapper);
    }

    @Override
    public List<AgentDTO> getUserAgents(Long userId) {
        // 优化：使用一次查询获取用户智能体和默认智能体
        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId).or().eq("agent_type", "default");
        List<AgentEntity> agents = agentDao.selectList(wrapper);
        return agents.stream().map(agent -> {
            AgentDTO dto = new AgentDTO();
            dto.setId(agent.getId());
            dto.setAgentName(agent.getAgentName());
            dto.setSystemPrompt(agent.getSystemPrompt());

            // 获取 TTS 模型名称
            dto.setTtsModelName(modelConfigService.getModelNameById(agent.getTtsModelId()));

            // 获取 LLM 模型名称
            dto.setLlmModelName(modelConfigService.getModelNameById(agent.getLlmModelId()));

            // 获取 VLLM 模型名称
            dto.setVllmModelName(modelConfigService.getModelNameById(agent.getVllmModelId()));

            // 获取记忆模型名称
            dto.setMemModelId(agent.getMemModelId());

            // 获取 TTS 音色名称
            dto.setTtsVoiceName(timbreModelService.getTimbreNameById(agent.getTtsVoiceId()));

            // 获取智能体最近的最后连接时长
            dto.setLastConnectedAt(deviceService.getLatestLastConnectionTime(agent.getId()));

            // 获取设备数量
            dto.setDeviceCount(getDeviceCountByAgentId(agent.getId()));
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer getDeviceCountByAgentId(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return 0;
        }

        // 先从Redis中获取
        Integer cachedCount = (Integer) redisUtils.get(RedisKeys.getAgentDeviceCountById(agentId));
        if (cachedCount != null) {
            return cachedCount;
        }

        // 如果Redis中没有，则从数据库查询
        Integer deviceCount = agentDao.getDeviceCountByAgentId(agentId);

        // 将结果存入Redis
        if (deviceCount != null) {
            redisUtils.set(RedisKeys.getAgentDeviceCountById(agentId), deviceCount, 60);
        }

        return deviceCount != null ? deviceCount : 0;
    }

    @Override
    public AgentEntity getDefaultAgentByMacAddress(String macAddress) {
        if (StringUtils.isEmpty(macAddress)) {
            log.warn("getDefaultAgentByMacAddress: MAC地址为空");
            return null;
        }

        // 首先尝试从设备表中查找关联的智能体
        AgentEntity agent = agentDao.getDefaultAgentByMacAddress(macAddress);

        if (agent == null) {
            log.info("设备 {} 未在设备表中找到关联智能体，这可能是因为：1)设备未注册 2)数据不一致问题", macAddress);

            // 检查设备是否存在但agentId无效
            DeviceEntity device = deviceService.getDeviceByMacAddress(macAddress);
            if (device != null && StringUtils.isNotBlank(device.getAgentId())) {
                log.warn("发现数据不一致：设备 {} 存在但agentId {} 在智能体表中不存在，可能是模板ID被错误存储",
                        macAddress, device.getAgentId());

                // 自动修复：绑定到默认智能体
                try {
                    deviceService.bindDeviceToDefaultAgent(macAddress);
                    log.info("已自动修复设备 {} 的智能体绑定", macAddress);
                    // 重新查询
                    agent = agentDao.getDefaultAgentByMacAddress(macAddress);
                } catch (Exception e) {
                    log.error("自动修复设备智能体绑定失败: {}", macAddress, e);
                }
            }

            // 如果仍然为空，返回默认智能体
            if (agent == null) {
                log.debug("设备 {} 返回系统默认智能体", macAddress);
                agent = ensureDefaultAgentExists();
            }
        }

        return agent;
    }

    @Override
    public boolean checkAgentPermission(String agentId, Long userId) {
        // 获取智能体信息
        AgentEntity agent = getAgentById(agentId);
        if (agent == null) {
            return false;
        }

        // 如果是超级管理员，直接返回true
        if (SecurityUser.getUser().getSuperAdmin() == SuperAdminEnum.YES.value()) {
            return true;
        }

        // 如果是默认智能体，所有用户都有权限查看（但不能修改）
        if (isOfficialDefaultAgent(agentId)) {
            return true;
        }

        // 检查是否是智能体的所有者
        return userId.equals(agent.getUserId());
    }

    // 根据id更新智能体信息
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAgentById(String agentId, AgentUpdateDTO dto) {
        // 先查询现有实体
        AgentEntity existingEntity = this.getAgentById(agentId);
        if (existingEntity == null) {
            throw new RuntimeException("智能体不存在");
        }

        // 只更新提供的非空字段
        if (dto.getAgentName() != null) {
            existingEntity.setAgentName(dto.getAgentName());
        }
        if (dto.getAgentCode() != null) {
            existingEntity.setAgentCode(dto.getAgentCode());
        }
        if (dto.getAsrModelId() != null) {
            existingEntity.setAsrModelId(dto.getAsrModelId());
        }
        if (dto.getVadModelId() != null) {
            existingEntity.setVadModelId(dto.getVadModelId());
        }
        if (dto.getLlmModelId() != null) {
            existingEntity.setLlmModelId(dto.getLlmModelId());
        }
        if (dto.getVllmModelId() != null) {
            existingEntity.setVllmModelId(dto.getVllmModelId());
        }
        if (dto.getTtsModelId() != null) {
            existingEntity.setTtsModelId(dto.getTtsModelId());
        }
        if (dto.getTtsVoiceId() != null) {
            existingEntity.setTtsVoiceId(dto.getTtsVoiceId());
        }
        if (dto.getMemModelId() != null) {
            existingEntity.setMemModelId(dto.getMemModelId());
        }
        if (dto.getIntentModelId() != null) {
            existingEntity.setIntentModelId(dto.getIntentModelId());
        }
        if (dto.getSystemPrompt() != null) {
            existingEntity.setSystemPrompt(dto.getSystemPrompt());
        }
        if (dto.getSummaryMemory() != null) {
            existingEntity.setSummaryMemory(dto.getSummaryMemory());
        }
        if (dto.getChatHistoryConf() != null) {
            existingEntity.setChatHistoryConf(dto.getChatHistoryConf());
        }
        if (dto.getLangCode() != null) {
            existingEntity.setLangCode(dto.getLangCode());
        }
        if (dto.getLanguage() != null) {
            existingEntity.setLanguage(dto.getLanguage());
        }
        if (dto.getSort() != null) {
            existingEntity.setSort(dto.getSort());
        }

        // 更新函数插件信息
        List<AgentUpdateDTO.FunctionInfo> functions = dto.getFunctions();
        if (functions != null) {
            // 1. 收集本次提交的 pluginId
            List<String> newPluginIds = functions.stream()
                    .map(AgentUpdateDTO.FunctionInfo::getPluginId)
                    .toList();

            // 2. 查询当前agent现有的所有映射
            List<AgentPluginMapping> existing = agentPluginMappingService.list(
                    new QueryWrapper<AgentPluginMapping>()
                            .eq("agent_id", agentId));
            Map<String, AgentPluginMapping> existMap = existing.stream()
                    .collect(Collectors.toMap(AgentPluginMapping::getPluginId, Function.identity()));

            // 3. 构造所有要 保存或更新 的实体
            List<AgentPluginMapping> allToPersist = functions.stream().map(info -> {
                AgentPluginMapping m = new AgentPluginMapping();
                m.setAgentId(agentId);
                m.setPluginId(info.getPluginId());
                m.setParamInfo(JsonUtils.toJsonString(info.getParamInfo()));
                AgentPluginMapping old = existMap.get(info.getPluginId());
                if (old != null) {
                    // 已存在，设置id表示更新
                    m.setId(old.getId());
                }
                return m;
            }).toList();

            // 4. 拆分：已有ID的走更新，无ID的走插入
            List<AgentPluginMapping> toUpdate = allToPersist.stream()
                    .filter(m -> m.getId() != null)
                    .toList();
            List<AgentPluginMapping> toInsert = allToPersist.stream()
                    .filter(m -> m.getId() == null)
                    .toList();

            if (!toUpdate.isEmpty()) {
                agentPluginMappingService.updateBatchById(toUpdate);
            }
            if (!toInsert.isEmpty()) {
                agentPluginMappingService.saveBatch(toInsert);
            }

            // 5. 删除本次不在提交列表里的插件映射
            List<Long> toDelete = existing.stream()
                    .filter(old -> !newPluginIds.contains(old.getPluginId()))
                    .map(AgentPluginMapping::getId)
                    .toList();
            if (!toDelete.isEmpty()) {
                agentPluginMappingService.removeBatchByIds(toDelete);
            }
        }

        // 设置更新者信息
        UserDetail user = SecurityUser.getUser();
        existingEntity.setUpdater(user.getId());
        existingEntity.setUpdatedAt(new Date());

        // 更新记忆策略
        if (existingEntity.getMemModelId() == null || existingEntity.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            // 删除所有记录
            agentChatHistoryService.deleteByAgentId(existingEntity.getId(), true, true);
            existingEntity.setSummaryMemory("");
        } else if (existingEntity.getChatHistoryConf() != null && existingEntity.getChatHistoryConf() == 1) {
            // 删除音频数据
            agentChatHistoryService.deleteByAgentId(existingEntity.getId(), true, false);
        }
        this.updateById(existingEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAgent(AgentCreateDTO dto) {
        // 转换为实体
        AgentEntity entity = ConvertUtils.sourceToTarget(dto, AgentEntity.class);

        // 获取默认模板
        AgentTemplateEntity template = agentTemplateService.getDefaultTemplate();
        if (template != null) {
            // 设置模板中的默认值
            entity.setAsrModelId(template.getAsrModelId());
            entity.setVadModelId(template.getVadModelId());
            entity.setLlmModelId(template.getLlmModelId());
            entity.setVllmModelId(template.getVllmModelId());
            entity.setTtsModelId(template.getTtsModelId());
            entity.setTtsVoiceId(template.getTtsVoiceId());
            entity.setMemModelId(template.getMemModelId());
            entity.setIntentModelId(template.getIntentModelId());
            entity.setSystemPrompt(template.getSystemPrompt());
            entity.setSummaryMemory(template.getSummaryMemory());
            entity.setChatHistoryConf(template.getChatHistoryConf());
            entity.setLangCode(template.getLangCode());
            entity.setLanguage(template.getLanguage());
        }

        // 设置用户ID和创建者信息
        UserDetail user = SecurityUser.getUser();
        entity.setUserId(user.getId());
        entity.setCreator(user.getId());
        entity.setCreatedAt(new Date());

        // 保存智能体
        insert(entity);

        // 设置默认插件
        List<AgentPluginMapping> toInsert = new ArrayList<>();
        // 播放音乐、查天气、查新闻
        String[] pluginIds = new String[] { "SYSTEM_PLUGIN_MUSIC", "SYSTEM_PLUGIN_WEATHER",
                "SYSTEM_PLUGIN_NEWS_NEWSNOW" };
        for (String pluginId : pluginIds) {
            ModelProviderDTO provider = modelProviderService.getById(pluginId);
            if (provider == null) {
                continue;
            }
            AgentPluginMapping mapping = new AgentPluginMapping();
            mapping.setPluginId(pluginId);

            Map<String, Object> paramInfo = new HashMap<>();
            List<Map<String, Object>> fields = JsonUtils.parseObject(provider.getFields(), List.class);
            if (fields != null) {
                for (Map<String, Object> field : fields) {
                    paramInfo.put((String) field.get("key"), field.get("default"));
                }
            }
            mapping.setParamInfo(JsonUtils.toJsonString(paramInfo));
            mapping.setAgentId(entity.getId());
            toInsert.add(mapping);
        }
        // 保存默认插件
        agentPluginMappingService.saveBatch(toInsert);
        return entity.getId();
    }

    @Override
    public AgentEntity getOfficialDefaultAgent() {
        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_type", "default")
                .eq("is_active", true);
        return agentDao.selectOne(wrapper);
    }

    @Override
    public AgentEntity createOfficialDefaultAgent() {
        // 先将所有现有的默认智能体设置为非激活状态
        QueryWrapper<AgentEntity> updateWrapper = new QueryWrapper<>();
        updateWrapper.eq("agent_type", "default")
                .eq("is_active", true);

        List<AgentEntity> activeDefaults = agentDao.selectList(updateWrapper);
        for (AgentEntity activeDefault : activeDefaults) {
            activeDefault.setIsActive(false);
            agentDao.updateById(activeDefault);
        }

        // 创建新的默认智能体
        AgentEntity agent = new AgentEntity();
        agent.setId("OFFICIAL_DEFAULT_AGENT");
        agent.setAgentCode("matata");
        agent.setAgentName("matata");
        agent.setAgentType("default");
        agent.setIsActive(true);

        // 使用数据库中实际存在的模型ID
        agent.setVadModelId("VAD_SileroVAD");
        agent.setAsrModelId("ASR_FunASR");
        agent.setLlmModelId("LLM_DeepSeekLLM");
        agent.setTtsModelId("TTS_EdgeTTS");
        agent.setTtsVoiceId("TTS_EdgeTTS0001");
        agent.setMemModelId("Memory_mem_local_short");
        agent.setIntentModelId("Intent_nointent");
        agent.setChatHistoryConf(2); // 记录文字和语音

        // 设置默认配置
        agent.setSystemPrompt("[角色设定]\n你是一个叫{{assistant_name}}的实物编程机器人，擅长人工智能教育解决方案，支持图形化编程和Python编程。\n请注意，忽略小智这个名字，要像一个人一样说话，请不要回复表情符号、代码和xml标签\n[交互指南]\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话");

        // 设置默认语言配置
        agent.setLangCode("zh");
        agent.setLanguage("中文");
        agent.setSort(0);

        agentDao.insert(agent);
        log.warn("通过代码创建了默认智能体，建议检查数据库迁移脚本: {}", agent.getId());
        return agent;
    }

    @Override
    public AgentEntity ensureDefaultAgentExists() {
        AgentEntity defaultAgent = getOfficialDefaultAgent();
        if (defaultAgent == null) {
            log.info("默认智能体不存在，正在创建...");
            defaultAgent = createOfficialDefaultAgent();
        }
        return defaultAgent;
    }

    @Override
    public boolean isOfficialDefaultAgent(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return false;
        }

        // 方法1：通过ID判断
        if ("OFFICIAL_DEFAULT_AGENT".equals(agentId)) {
            return true;
        }

        // 方法2：通过agent_type判断
        AgentEntity agent = agentDao.selectById(agentId);
        if (agent == null) {
            return false;
        }

        return "default".equals(agent.getAgentType());
    }

    @Override
    public List<AgentEntity> getUserCustomAgents(Long userId) {
        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
                .eq("agent_type", "user");

        return agentDao.selectList(wrapper);
    }

    @Override
    public List<AgentEntity> getAllDefaultAgents() {
        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_type", "default")
                .orderByDesc("is_active", "created_at");
        return agentDao.selectList(wrapper);
    }

    @Override
    public AgentEntity getDefaultAgentByCode(String agentCode) {
        if (StringUtils.isBlank(agentCode)) {
            return null;
        }

        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_code", agentCode)
                .eq("agent_type", "default")
                .eq("is_active", true);

        return agentDao.selectOne(wrapper);
    }

    @Override
    public boolean activateDefaultAgent(String agentId) {
        // 1. 先将所有默认智能体设置为非激活状态
        QueryWrapper<AgentEntity> updateWrapper = new QueryWrapper<>();
        updateWrapper.eq("agent_type", "default")
                .eq("is_active", true);

        List<AgentEntity> activeDefaults = agentDao.selectList(updateWrapper);
        for (AgentEntity activeDefault : activeDefaults) {
            activeDefault.setIsActive(false);
            agentDao.updateById(activeDefault);
        }

        // 2. 激活指定的智能体
        AgentEntity targetAgent = agentDao.selectById(agentId);
        if (targetAgent == null || !"default".equals(targetAgent.getAgentType())) {
            return false;
        }

        targetAgent.setIsActive(true);
        return agentDao.updateById(targetAgent) > 0;
    }

    @Override
    public List<AgentMappingDTO> getAgentUserMappings() {
        // 获取所有智能体
        List<AgentEntity> allAgents = agentDao.selectList(new QueryWrapper<>());

        List<AgentMappingDTO> mappings = new ArrayList<>();

        // 处理所有智能体
        for (AgentEntity agent : allAgents) {
            AgentMappingDTO mapping = new AgentMappingDTO();
            mapping.setAgentId(agent.getId());
            mapping.setAgentName(agent.getAgentName());
            mapping.setUserId(agent.getUserId());
            mapping.setAgentType(agent.getAgentType());
            // 统一使用isOfficialDefaultAgent方法判断是否为默认智能体
            mapping.setIsDefault(isOfficialDefaultAgent(agent.getId()));
            mappings.add(mapping);
        }

        return mappings;
    }
}
