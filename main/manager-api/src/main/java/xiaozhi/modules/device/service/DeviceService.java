package xiaozhi.modules.device.service;

import java.util.Date;
import java.util.List;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.device.dto.DevicePageUserDTO;
import xiaozhi.modules.device.dto.DeviceReportReqDTO;
import xiaozhi.modules.device.dto.DeviceReportRespDTO;
import xiaozhi.modules.device.dto.DeviceManualAddDTO;
import xiaozhi.modules.device.dto.DeviceMappingDTO;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.vo.UserShowDeviceListVO;
import xiaozhi.modules.agent.entity.AgentEntity;

public interface DeviceService extends BaseService<DeviceEntity> {

    /**
     * 检查设备是否激活
     */
    DeviceReportRespDTO checkDeviceActive(String macAddress, String clientId,
            DeviceReportReqDTO deviceReport);

    /**
     * 获取用户指定智能体的设备列表，
     */
    List<DeviceEntity> getUserDevices(Long userId, String agentId);

    /**
     * 解绑设备
     */
    void unbindDevice(Long userId, String deviceId);

    /**
     * 设备激活
     */
    Boolean deviceActivation(String agentId, String activationCode);

    /**
     * 删除此用户的所有设备
     * 
     * @param userId 用户id
     */
    void deleteByUserId(Long userId);

    /**
     * 删除指定智能体关联的所有设备
     * 
     * @param agentId 智能体id
     */
    void deleteByAgentId(String agentId);

    /**
     * 获取指定用户的设备数量
     * 
     * @param userId 用户id
     * @return 设备数量
     */
    Long selectCountByUserId(Long userId);

    /**
     * 分页获取全部设备信息
     *
     * @param dto 分页查找参数
     * @return 用户列表分页数据
     */
    PageData<UserShowDeviceListVO> page(DevicePageUserDTO dto);

    /**
     * 根据MAC地址获取设备信息
     * 
     * @param macAddress MAC地址
     * @return 设备信息
     */
    DeviceEntity getDeviceByMacAddress(String macAddress);

    /**
     * 根据设备ID获取激活码
     * 
     * @param deviceId 设备ID
     * @return 激活码
     */
    String geCodeByDeviceId(String deviceId);

    /**
     * 获取这个智能体设备理的最近的最后连接时间
     * @param agentId 智能体id
     * @return 返回设备最近的最后连接时间
     */
    Date getLatestLastConnectionTime(String agentId);

    /**
     * 手动添加设备
     */
    void manualAddDevice(Long userId, DeviceManualAddDTO dto);

    /**
     * 更新设备连接信息
     */
    void updateDeviceConnectionInfo(String agentId, String deviceId, String appVersion);

    /**
     * 获取所有设备-智能体映射关系
     * 
     * @return 设备-智能体映射列表
     */
    List<DeviceMappingDTO> getDeviceAgentMappings();

    /**
     * 根据MAC地址获取设备-智能体映射
     * @param macAddress MAC地址
     * @return 设备-智能体映射
     */
    DeviceMappingDTO getDeviceMappingByMac(String macAddress);

    /**
     * 绑定设备到默认智能体
     *
     * @param deviceMac 设备MAC地址
     * @return 绑定结果
     */
    boolean bindDeviceToDefaultAgent(String deviceMac);

    /**
     * 切换设备智能体
     *
     * @param deviceMac 设备MAC地址
     * @param newAgentId 新智能体ID
     * @param userId 用户ID(用于权限验证)
     * @return 切换结果
     */
    boolean switchDeviceAgent(String deviceMac, String newAgentId, Long userId);

    /**
     * 获取官方默认智能体
     *
     * @return 官方默认智能体
     */
    AgentEntity getOfficialDefaultAgent();

    /**
     * 创建官方默认智能体
     *
     * @return 新创建的官方默认智能体
     */
    AgentEntity createOfficialDefaultAgent();

    /**
     * 获取所有已注册设备的MAC地址列表
     *
     * @return MAC地址列表
     */
    List<String> getAllRegisteredMacAddresses();

    /**
     * 获取所有启用设备的MAC地址列表
     *
     * @return MAC地址列表
     */
    List<String> getAllEnabledMacAddresses();

    /**
     * 检查MAC地址对应的设备是否启用
     *
     * @param macAddress MAC地址
     * @return 是否启用
     */
    boolean isDeviceEnabled(String macAddress);

    /**
     * 启用/禁用设备
     *
     * @param macAddress MAC地址
     * @param enabled 是否启用
     * @return 操作结果
     */
    boolean setDeviceEnabled(String macAddress, boolean enabled);

    /**
     * 批量导入设备
     *
     * @param macAddresses MAC地址列表
     * @param registerSource 注册来源
     * @return 成功导入数量
     */
    int batchImportDevices(List<String> macAddresses, String registerSource);

}