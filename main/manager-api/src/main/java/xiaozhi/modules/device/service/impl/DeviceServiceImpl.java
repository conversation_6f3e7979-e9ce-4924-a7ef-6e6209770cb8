package xiaozhi.modules.device.service.impl;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import cn.hutool.core.util.RandomUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.PageData;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.common.utils.DateUtils;
import xiaozhi.modules.device.dao.DeviceDao;
import xiaozhi.modules.device.dto.DevicePageUserDTO;
import xiaozhi.modules.device.dto.DeviceReportReqDTO;
import xiaozhi.modules.device.dto.DeviceReportRespDTO;
import xiaozhi.modules.device.dto.DeviceMappingDTO;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.entity.OtaEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.device.service.OtaService;
import xiaozhi.modules.device.vo.UserShowDeviceListVO;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.sys.service.SysParamsService;
import xiaozhi.modules.sys.service.SysUserUtilService;
import xiaozhi.modules.device.dto.DeviceManualAddDTO;
import xiaozhi.modules.agent.dao.AgentDao;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.agent.service.AgentService;

import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.UUID;

@Slf4j
@Service
@AllArgsConstructor
public class DeviceServiceImpl extends BaseServiceImpl<DeviceDao, DeviceEntity> implements DeviceService {

    private final DeviceDao deviceDao;
    private final SysUserUtilService sysUserUtilService;
    private final SysParamsService sysParamsService;
    private final RedisUtils redisUtils;
    private final OtaService otaService;
    private final AgentDao agentDao;

    @Async
    public void updateDeviceConnectionInfo(String agentId, String deviceId, String appVersion) {
        try {
            DeviceEntity device = new DeviceEntity();
            device.setId(deviceId);
            device.setLastConnectedAt(new Date());
            if (StringUtils.isNotBlank(appVersion)) {
                device.setAppVersion(appVersion);
            }
            deviceDao.updateById(device);
            if (StringUtils.isNotBlank(agentId)) {
                redisUtils.set(RedisKeys.getAgentDeviceLastConnectedAtById(agentId), new Date());
            }
        } catch (Exception e) {
            log.error("异步更新设备连接信息失败", e);
        }
    }

    @Override
    public Boolean deviceActivation(String agentId, String activationCode) {
        if (StringUtils.isBlank(activationCode)) {
            throw new RenException("激活码不能为空");
        }
        String deviceKey = "ota:activation:code:" + activationCode;
        Object cacheDeviceId = redisUtils.get(deviceKey);
        if (cacheDeviceId == null) {
            throw new RenException("激活码错误");
        }
        String deviceId = (String) cacheDeviceId;
        String safeDeviceId = deviceId.replace(":", "_").toLowerCase();
        String cacheDeviceKey = String.format("ota:activation:data:%s", safeDeviceId);
        Map<String, Object> cacheMap = (Map<String, Object>) redisUtils.get(cacheDeviceKey);
        if (cacheMap == null) {
            throw new RenException("激活码错误");
        }
        String cachedCode = (String) cacheMap.get("activation_code");
        if (!activationCode.equals(cachedCode)) {
            throw new RenException("激活码错误");
        }
        // 检查设备有没有被激活
        if (selectById(deviceId) != null) {
            throw new RenException("设备已激活");
        }

        String macAddress = (String) cacheMap.get("mac_address");
        String board = (String) cacheMap.get("board");
        String appVersion = (String) cacheMap.get("app_version");
        UserDetail user = SecurityUser.getUser();
        if (user.getId() == null) {
            throw new RenException("用户未登录");
        }

        Date currentTime = new Date();
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setId(deviceId);
        deviceEntity.setBoard(board);
        deviceEntity.setAgentId(agentId);
        deviceEntity.setAppVersion(appVersion);
        deviceEntity.setMacAddress(macAddress);
        deviceEntity.setUserId(user.getId());
        deviceEntity.setCreator(user.getId());
        deviceEntity.setAutoUpdate(1);
        deviceEntity.setCreateDate(currentTime);
        deviceEntity.setUpdater(user.getId());
        deviceEntity.setUpdateDate(currentTime);
        deviceEntity.setLastConnectedAt(currentTime);
        deviceDao.insert(deviceEntity);

        // 清理redis缓存
        redisUtils.delete(cacheDeviceKey);
        redisUtils.delete(deviceKey);
        return true;
    }

    @Override
    public DeviceReportRespDTO checkDeviceActive(String macAddress, String clientId,
            DeviceReportReqDTO deviceReport) {
        DeviceReportRespDTO response = new DeviceReportRespDTO();
        response.setServer_time(buildServerTime());

        DeviceEntity deviceById = getDeviceByMacAddress(macAddress);

        // 设备未绑定，则返回当前上传的固件信息（不更新）以此兼容旧固件版本
        if (deviceById == null) {
            DeviceReportRespDTO.Firmware firmware = new DeviceReportRespDTO.Firmware();
            firmware.setVersion(deviceReport.getApplication().getVersion());
            firmware.setUrl(Constant.INVALID_FIRMWARE_URL);
            response.setFirmware(firmware);
        } else {
            // 只有在设备已绑定且autoUpdate不为0的情况下才返回固件升级信息
            if (deviceById.getAutoUpdate() != 0) {
                String type = deviceReport.getBoard() == null ? null : deviceReport.getBoard().getType();
                DeviceReportRespDTO.Firmware firmware = buildFirmwareInfo(type,
                        deviceReport.getApplication() == null ? null : deviceReport.getApplication().getVersion());
                response.setFirmware(firmware);
            }
        }

        // 添加WebSocket配置
        DeviceReportRespDTO.Websocket websocket = new DeviceReportRespDTO.Websocket();
        // 从系统参数获取WebSocket URL，如果未配置则使用默认值
        String wsUrl = sysParamsService.getValue(Constant.SERVER_WEBSOCKET, true);
        if (StringUtils.isBlank(wsUrl) || wsUrl.equals("null")) {
            log.error("WebSocket地址未配置，请登录智控台，在参数管理找到【server.websocket】配置");
            wsUrl = "ws://chat-api.matatalab.com/xiaozhi/v1/";
            websocket.setUrl(wsUrl);
        } else {
            String[] wsUrls = wsUrl.split("\\;");
            if (wsUrls.length > 0) {
                // 随机选择一个WebSocket URL
                websocket.setUrl(wsUrls[RandomUtil.randomInt(0, wsUrls.length)]);
            } else {
                log.error("WebSocket地址未配置，请登录智控台，在参数管理找到【server.websocket】配置");
                websocket.setUrl("ws://chat-api.matatalab.com/xiaozhi/v1/");
            }
        }

        response.setWebsocket(websocket);

        if (deviceById != null) {
            // 如果设备存在，则异步更新上次连接时间和版本信息
            String appVersion = deviceReport.getApplication() != null ? deviceReport.getApplication().getVersion()
                    : null;
            // 通过Spring代理调用异步方法
            ((DeviceServiceImpl) AopContext.currentProxy()).updateDeviceConnectionInfo(deviceById.getAgentId(),
                    deviceById.getId(), appVersion);
        } else {
            // 检查MAC认证配置，决定是否返回激活码
            String macAuthEnabledStr = sysParamsService.getValue("mac_auth.enabled", true);
            boolean macAuthEnabled = "true".equalsIgnoreCase(macAuthEnabledStr);

            if (!macAuthEnabled) {
                // MAC认证未启用，使用Token认证模式，返回激活码
                DeviceReportRespDTO.Activation code = buildActivation(macAddress, deviceReport);
                response.setActivation(code);
                log.info("设备 {} 不存在，Token认证模式下返回激活码", macAddress);
            } else {
                // MAC认证已启用，检查是否开启自动注册
                String autoRegisterParam = sysParamsService.getValue("mac_auth.auto_register", false);
                boolean autoRegisterEnabled = "true".equalsIgnoreCase(autoRegisterParam);

                if (autoRegisterEnabled) {
                    // 开启自动注册时，缓存设备信息供后续自动注册使用
                    cacheDeviceInfo(macAddress, deviceReport);
                    log.info("设备 {} 不存在，MAC认证模式下已缓存设备信息，由Python服务处理自动注册", macAddress);
                } else {
                    log.info("设备 {} 不存在，MAC认证模式下不返回激活码，由Python服务处理", macAddress);
                }
            }
        }

        return response;
    }

    @Override
    public List<DeviceEntity> getUserDevices(Long userId, String agentId) {
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();

        // 直接根据默认智能体ID判断是否为默认智能体
        boolean isDefaultAgent = "OFFICIAL_DEFAULT_AGENT".equals(agentId);

        // 检查当前用户是否为管理员
        UserDetail currentUser = SecurityUser.getUser();
        boolean isAdmin = currentUser != null && currentUser.getSuperAdmin() == 1;

        if (isDefaultAgent) {
            // 对于默认智能体，基于ai_device表的实际数据结构
            wrapper.eq("agent_id", agentId);

            if (isAdmin) {
                // 管理员可以查看所有默认智能体的设备
                // 不添加user_id限制，可以看到所有绑定到默认智能体的设备
                log.debug("管理员查询默认智能体所有设备: agentId={}, userId={}", agentId, userId);
            } else {
                // 普通用户只能查看自己的设备
                // 根据ai_device表结构，设备可能有user_id（用户绑定）或user_id为null（MAC验证）
                wrapper.eq("user_id", userId);
                log.debug("普通用户查询默认智能体自己的设备: agentId={}, userId={}", agentId, userId);
            }
        } else {
            // 对于用户智能体，按原逻辑查询（只能查看自己的设备）
            wrapper.eq("user_id", userId);
            wrapper.eq("agent_id", agentId);
            log.debug("查询用户智能体设备: agentId={}, userId={}", agentId, userId);
        }

        return baseDao.selectList(wrapper);
    }

    @Override
    public void unbindDevice(Long userId, String deviceId) {
        // 检查当前用户是否为管理员
        UserDetail currentUser = SecurityUser.getUser();
        boolean isAdmin = currentUser != null && currentUser.getSuperAdmin() == 1;

        // 查询设备信息
        DeviceEntity device = baseDao.selectById(deviceId);
        if (device == null) {
            return;
        }

        // 如果是管理员且是默认智能体，允许解绑
        if (isAdmin && "OFFICIAL_DEFAULT_AGENT".equals(device.getAgentId())) {
            UpdateWrapper<DeviceEntity> wrapper = new UpdateWrapper<>();
            wrapper.eq("id", deviceId);
            baseDao.delete(wrapper);
        } else {
            // 否则走原逻辑
            UpdateWrapper<DeviceEntity> wrapper = new UpdateWrapper<>();
            wrapper.eq("user_id", userId);
            wrapper.eq("id", deviceId);
            baseDao.delete(wrapper);
        }
    }

    @Override
    public void deleteByUserId(Long userId) {
        UpdateWrapper<DeviceEntity> wrapper = new UpdateWrapper<>();
        wrapper.eq("user_id", userId);
        baseDao.delete(wrapper);
    }

    @Override
    public Long selectCountByUserId(Long userId) {
        UpdateWrapper<DeviceEntity> wrapper = new UpdateWrapper<>();
        wrapper.eq("user_id", userId);
        return baseDao.selectCount(wrapper);
    }

    @Override
    public void deleteByAgentId(String agentId) {
        UpdateWrapper<DeviceEntity> wrapper = new UpdateWrapper<>();
        wrapper.eq("agent_id", agentId);
        baseDao.delete(wrapper);
    }

    @Override
    public PageData<UserShowDeviceListVO> page(DevicePageUserDTO dto) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put(Constant.PAGE, dto.getPage());
        params.put(Constant.LIMIT, dto.getLimit());
        IPage<DeviceEntity> page = baseDao.selectPage(
                getPage(params, "mac_address", true),
                // 定义查询条件
                new QueryWrapper<DeviceEntity>()
                        // 必须设备关键词查找
                        .like(StringUtils.isNotBlank(dto.getKeywords()), "alias", dto.getKeywords()));
        // 循环处理page获取回来的数据，返回需要的字段
        List<UserShowDeviceListVO> list = page.getRecords().stream().map(device -> {
            UserShowDeviceListVO vo = ConvertUtils.sourceToTarget(device, UserShowDeviceListVO.class);
            // 把最后修改的时间，改为简短描述的时间
            vo.setRecentChatTime(DateUtils.getShortTime(device.getUpdateDate()));
            sysUserUtilService.assignUsername(device.getUserId(),
                    vo::setBindUserName);
            vo.setDeviceType(device.getBoard());
            return vo;
        }).toList();
        // 计算页数
        return new PageData<>(list, page.getTotal());
    }

    @Override
    public DeviceEntity getDeviceByMacAddress(String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return null;
        }
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("mac_address", macAddress);
        return baseDao.selectOne(wrapper);
    }

    private DeviceReportRespDTO.ServerTime buildServerTime() {
        DeviceReportRespDTO.ServerTime serverTime = new DeviceReportRespDTO.ServerTime();
        TimeZone tz = TimeZone.getDefault();
        serverTime.setTimestamp(Instant.now().toEpochMilli());
        serverTime.setTimeZone(tz.getID());
        serverTime.setTimezone_offset(tz.getOffset(System.currentTimeMillis()) / (60 * 1000));
        return serverTime;
    }

    @Override
    public String geCodeByDeviceId(String deviceId) {
        String dataKey = getDeviceCacheKey(deviceId);

        Map<String, Object> cacheMap = (Map<String, Object>) redisUtils.get(dataKey);
        if (cacheMap != null && cacheMap.containsKey("activation_code")) {
            String cachedCode = (String) cacheMap.get("activation_code");
            return cachedCode;
        }
        return null;
    }

    @Override
    public Date getLatestLastConnectionTime(String agentId) {
        // 查询是否有缓存时间，有则返回
        Date cachedDate = (Date) redisUtils.get(RedisKeys.getAgentDeviceLastConnectedAtById(agentId));
        if (cachedDate != null) {
            return cachedDate;
        }
        Date maxDate = deviceDao.getAllLastConnectedAtByAgentId(agentId);
        if (maxDate != null) {
            redisUtils.set(RedisKeys.getAgentDeviceLastConnectedAtById(agentId), maxDate);
        }
        return maxDate;
    }

    private String getDeviceCacheKey(String deviceId) {
        String safeDeviceId = deviceId.replace(":", "_").toLowerCase();
        String dataKey = String.format("ota:activation:data:%s", safeDeviceId);
        return dataKey;
    }

    /**
     * 缓存设备信息到Redis，供后续自动注册使用
     * @param deviceId 设备ID（MAC地址）
     * @param deviceReport 设备上报信息
     */
    private void cacheDeviceInfo(String deviceId, DeviceReportReqDTO deviceReport) {
        try {
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("id", deviceId);
            dataMap.put("mac_address", deviceId);

            String boardType = (deviceReport.getBoard() != null && deviceReport.getBoard().getType() != null)
                    ? deviceReport.getBoard().getType()
                    : (deviceReport.getChipModelName() != null ? deviceReport.getChipModelName() : "unknown");
            String appVersion = (deviceReport.getApplication() != null)
                    ? deviceReport.getApplication().getVersion()
                    : null;

            dataMap.put("board", boardType);
            dataMap.put("app_version", appVersion);
            dataMap.put("deviceId", deviceId);

            // 写入主数据 key
            String dataKey = getDeviceCacheKey(deviceId);
            redisUtils.set(dataKey, dataMap);

            log.info("已缓存设备信息到Redis - MAC地址: {}, 设备型号: {}, 应用版本: {}",
                    deviceId, boardType, appVersion);
        } catch (Exception e) {
            log.warn("缓存设备信息失败 - MAC地址: {}, 错误: {}", deviceId, e.getMessage());
        }
    }

    public DeviceReportRespDTO.Activation buildActivation(String deviceId, DeviceReportReqDTO deviceReport) {
        DeviceReportRespDTO.Activation code = new DeviceReportRespDTO.Activation();

        String cachedCode = geCodeByDeviceId(deviceId);

        if (StringUtils.isNotBlank(cachedCode)) {
            code.setCode(cachedCode);
            String frontedUrl = sysParamsService.getValue(Constant.SERVER_FRONTED_URL, true);
            code.setMessage(frontedUrl + "\n" + cachedCode);
            code.setChallenge(deviceId);
        } else {
            String newCode = RandomUtil.randomNumbers(6);
            code.setCode(newCode);
            String frontedUrl = sysParamsService.getValue(Constant.SERVER_FRONTED_URL, true);
            code.setMessage(frontedUrl + "\n" + newCode);
            code.setChallenge(deviceId);

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("id", deviceId);
            dataMap.put("mac_address", deviceId);

            dataMap.put("board", (deviceReport.getBoard() != null && deviceReport.getBoard().getType() != null)
                    ? deviceReport.getBoard().getType()
                    : (deviceReport.getChipModelName() != null ? deviceReport.getChipModelName() : "unknown"));
            dataMap.put("app_version", (deviceReport.getApplication() != null)
                    ? deviceReport.getApplication().getVersion()
                    : null);

            dataMap.put("deviceId", deviceId);
            dataMap.put("activation_code", newCode);

            // 写入主数据 key
            String dataKey = getDeviceCacheKey(deviceId);
            redisUtils.set(dataKey, dataMap);

            // 写入反查激活码 key
            String codeKey = "ota:activation:code:" + newCode;
            redisUtils.set(codeKey, deviceId);
        }
        return code;
    }

    private DeviceReportRespDTO.Firmware buildFirmwareInfo(String type, String currentVersion) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        if (StringUtils.isBlank(currentVersion)) {
            currentVersion = "0.0.0";
        }

        OtaEntity ota = otaService.getLatestOta(type);
        DeviceReportRespDTO.Firmware firmware = new DeviceReportRespDTO.Firmware();
        String downloadUrl = null;

        if (ota != null) {
            // 如果设备没有版本信息，或者OTA版本比设备版本新，则返回下载地址
            if (compareVersions(ota.getVersion(), currentVersion) > 0) {
                String otaUrl = sysParamsService.getValue(Constant.SERVER_OTA, true);
                if (StringUtils.isBlank(otaUrl) || otaUrl.equals("null")) {
                    log.error("OTA地址未配置，请登录智控台，在参数管理找到【server.ota】配置");
                    // 尝试从请求中获取
                    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder
                            .getRequestAttributes())
                            .getRequest();
                    otaUrl = request.getRequestURL().toString();
                }
                // 将URL中的/ota/替换为/otaMag/download/
                String uuid = UUID.randomUUID().toString();
                redisUtils.set(RedisKeys.getOtaIdKey(uuid), ota.getId());
                downloadUrl = otaUrl.replace("/ota/", "/otaMag/download/") + uuid;
            }
        }

        firmware.setVersion(ota == null ? currentVersion : ota.getVersion());
        firmware.setUrl(downloadUrl == null ? Constant.INVALID_FIRMWARE_URL : downloadUrl);
        return firmware;
    }

    /**
     * 比较两个版本号
     * 
     * @param version1 版本1
     * @param version2 版本2
     * @return 如果version1 > version2返回1，version1 < version2返回-1，相等返回0
     */
    private static int compareVersions(String version1, String version2) {
        if (version1 == null || version2 == null) {
            return 0;
        }

        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        int length = Math.max(v1Parts.length, v2Parts.length);
        for (int i = 0; i < length; i++) {
            int v1 = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2 = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1 > v2) {
                return 1;
            } else if (v1 < v2) {
                return -1;
            }
        }
        return 0;
    }

    @Override
    public void manualAddDevice(Long userId, DeviceManualAddDTO dto) {
        // 检查mac是否已存在
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("mac_address", dto.getMacAddress());
        DeviceEntity exist = baseDao.selectOne(wrapper);
        if (exist != null) {
            throw new RenException("该Mac地址已存在");
        }
        Date now = new Date();
        DeviceEntity entity = new DeviceEntity();
        entity.setId(UUID.randomUUID().toString().replace("-", ""));
        entity.setUserId(userId);
        entity.setAgentId(dto.getAgentId());
        entity.setBoard(dto.getBoard());
        entity.setAppVersion(dto.getAppVersion());
        entity.setMacAddress(dto.getMacAddress());
        entity.setCreateDate(now);
        entity.setUpdateDate(now);
        entity.setLastConnectedAt(now);
        entity.setCreator(userId);
        entity.setUpdater(userId);
        entity.setAutoUpdate(1);
        baseDao.insert(entity);
    }

    @Override
    public List<DeviceMappingDTO> getDeviceAgentMappings() {
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.isNotNull("agent_id")
               .isNotNull("mac_address");
        
        List<DeviceEntity> devices = baseDao.selectList(wrapper);
        
        return devices.stream().map(device -> {
            DeviceMappingDTO mapping = new DeviceMappingDTO();
            mapping.setMacAddress(device.getMacAddress());
            mapping.setAgentId(device.getAgentId());
            mapping.setUserId(device.getUserId());
            mapping.setAlias(device.getAlias());
            mapping.setStatus(1); // 默认状态为启用
            return mapping;
        }).toList();
    }

    @Override
    public DeviceMappingDTO getDeviceMappingByMac(String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return null;
        }
        String redisKey = "device:mapping:" + macAddress.toLowerCase();
        DeviceMappingDTO cached = (DeviceMappingDTO) redisUtils.get(redisKey);
        if (cached != null) {
            return cached;
        }
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("mac_address", macAddress);
        DeviceEntity device = baseDao.selectOne(wrapper);
        if (device == null) {
            return null;
        }
        DeviceMappingDTO mapping = new DeviceMappingDTO();
        mapping.setMacAddress(device.getMacAddress());
        mapping.setAgentId(device.getAgentId());
        mapping.setUserId(device.getUserId());
        mapping.setAlias(device.getAlias());
        mapping.setStatus(1);
        redisUtils.set(redisKey, mapping, 600); // 缓存10分钟
        return mapping;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindDeviceToDefaultAgent(String deviceMac) {
        // 直接查询官方默认智能体
        AgentEntity defaultAgent = getOfficialDefaultAgent();
        if (defaultAgent == null) {
            defaultAgent = createOfficialDefaultAgent();
        }

        // 检查设备是否存在
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("mac_address", deviceMac);
        DeviceEntity device = deviceDao.selectOne(wrapper);

        if (device == null) {
            // 尝试从Redis缓存中获取设备信息
            String boardType = null;
            String appVersion = null;
            try {
                String cacheKey = getDeviceCacheKey(deviceMac);
                Object cachedData = redisUtils.get(cacheKey);
                if (cachedData instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> deviceInfo = (Map<String, Object>) cachedData;
                    boardType = (String) deviceInfo.get("board");
                    appVersion = (String) deviceInfo.get("app_version");
                    log.info("从Redis缓存获取设备信息 - MAC地址: {}, 设备型号: {}, 应用版本: {}",
                            deviceMac, boardType, appVersion);
                }
            } catch (Exception e) {
                log.warn("从Redis缓存获取设备信息失败 - MAC地址: {}, 错误: {}", deviceMac, e.getMessage());
            }

            // 创建新设备记录
            Date now = new Date();
            device = new DeviceEntity();
            device.setId(UUID.randomUUID().toString().replace("-", ""));
            device.setMacAddress(deviceMac);
            device.setAgentId(defaultAgent.getId());
            device.setUserId(null);
            device.setStatus(1); // 默认启用
            device.setRegisterSource("auto"); // 自动注册
            device.setRegisterTime(now);
            device.setAutoUpdate(1);
            device.setCreateDate(now);
            device.setUpdateDate(now);

            // 设置从缓存中获取的设备信息
            if (StringUtils.isNotBlank(boardType)) {
                device.setBoard(boardType);
            }
            if (StringUtils.isNotBlank(appVersion)) {
                device.setAppVersion(appVersion);
            }

            deviceDao.insert(device);

            log.info("自动注册成功，设备 {} 已创建并启用，设备型号: {}, 应用版本: {}",
                    deviceMac, device.getBoard(), device.getAppVersion());
        } else {
            // 更新现有设备的智能体绑定
            device.setAgentId(defaultAgent.getId());
            device.setUserId(null); // 清除直接用户关联
            device.setUpdateDate(new Date());
            deviceDao.updateById(device);

            log.info("设备 {} 已存在，更新智能体绑定为: {}", deviceMac, defaultAgent.getId());
        }

        // 清理相关缓存，确保下次认证时重新查询数据库
        clearDeviceRelatedCache(deviceMac);

        // 清理设备缓存
        String redisKey = "device:mapping:" + deviceMac.toLowerCase();
        redisUtils.delete(redisKey);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean switchDeviceAgent(String deviceMac, String newAgentId, Long userId) {
        // 验证设备存在性
        QueryWrapper<DeviceEntity> deviceWrapper = new QueryWrapper<>();
        deviceWrapper.eq("mac_address", deviceMac);
        DeviceEntity device = deviceDao.selectOne(deviceWrapper);
        
        if (device == null) {
            throw new RenException("设备不存在: " + deviceMac);
        }
        
        // 验证新智能体存在性和权限
        AgentEntity newAgent = agentDao.selectById(newAgentId);
        if (newAgent == null) {
            throw new RenException("智能体不存在: " + newAgentId);
        }
        
        // 权限检查
        if (newAgent.getUserId() != null && !newAgent.getUserId().equals(userId)) {
            throw new RenException("无权限绑定此智能体");
        }
        
        // 记录切换历史（这里可以扩展为记录切换日志）
        log.info("设备智能体切换: device={}, oldAgent={}, newAgent={}, user={}",
                deviceMac, device.getAgentId(), newAgentId, userId);
        
        // 执行切换
        String oldAgentId = device.getAgentId();
        device.setAgentId(newAgentId);
        device.setUpdateDate(new Date());
        deviceDao.updateById(device);
        
        // 清理相关缓存
        clearDeviceRelatedCache(deviceMac);

        return true;
    }

    @Override
    public AgentEntity getOfficialDefaultAgent() {
        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_type", "default")
               .eq("is_active", true);
        return agentDao.selectOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgentEntity createOfficialDefaultAgent() {
        // 先将所有现有的默认智能体设置为非激活状态
        QueryWrapper<AgentEntity> updateWrapper = new QueryWrapper<>();
        updateWrapper.eq("agent_type", "default")
                     .eq("is_active", true);

        List<AgentEntity> activeDefaults = agentDao.selectList(updateWrapper);
        for (AgentEntity activeDefault : activeDefaults) {
            activeDefault.setIsActive(false);
            agentDao.updateById(activeDefault);
        }

        // 创建新的默认智能体
        AgentEntity agent = new AgentEntity();
        agent.setId("OFFICIAL_DEFAULT_AGENT");
        agent.setAgentCode("matata");
        agent.setAgentName("matata");
        agent.setAgentType("default");
        agent.setIsActive(true);

        // 使用数据库中实际存在的模型ID
        agent.setVadModelId("VAD_SileroVAD");
        agent.setAsrModelId("ASR_FunASR");
        agent.setLlmModelId("LLM_DeepSeekLLM");
        agent.setTtsModelId("TTS_EdgeTTS");
        agent.setTtsVoiceId("TTS_EdgeTTS0001");
        agent.setMemModelId("Memory_mem_local_short");
        agent.setIntentModelId("Intent_nointent");
        agent.setChatHistoryConf(2); // 记录文字和语音

        // 设置默认配置
        agent.setSystemPrompt("[角色设定]\n你是一个叫{{assistant_name}}的实物编程机器人，擅长人工智能教育解决方案，支持图形化编程和Python编程。\n请注意，忽略小智这个名字，要像一个人一样说话，请不要回复表情符号、代码和xml标签\n[交互指南]\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话");

        // 设置默认语言配置
        agent.setLangCode("zh");
        agent.setLanguage("中文");
        agent.setSort(0);

        agentDao.insert(agent);
        log.warn("通过代码创建了默认智能体，建议检查数据库迁移脚本: {}", agent.getId());
        return agent;
    }

    @Override
    public List<String> getAllRegisteredMacAddresses() {
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.select("mac_address");
        wrapper.isNotNull("mac_address");
        wrapper.ne("mac_address", "");

        List<DeviceEntity> devices = baseDao.selectList(wrapper);
        return devices.stream()
                .map(DeviceEntity::getMacAddress)
                .filter(mac -> mac != null && !mac.trim().isEmpty())
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getAllEnabledMacAddresses() {
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.select("mac_address");
        wrapper.isNotNull("mac_address");
        wrapper.ne("mac_address", "");
        wrapper.eq("status", 1); // 只获取启用的设备

        List<DeviceEntity> devices = baseDao.selectList(wrapper);
        return devices.stream()
                .map(DeviceEntity::getMacAddress)
                .filter(mac -> mac != null && !mac.trim().isEmpty())
                .collect(Collectors.toList());
    }

    @Override
    public boolean isDeviceEnabled(String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return false;
        }

        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("mac_address", macAddress);
        wrapper.eq("status", 1);

        return baseDao.selectCount(wrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDeviceEnabled(String macAddress, boolean enabled) {
        if (StringUtils.isBlank(macAddress)) {
            return false;
        }

        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("mac_address", macAddress);
        DeviceEntity device = baseDao.selectOne(wrapper);

        if (device == null) {
            return false;
        }

        device.setStatus(enabled ? 1 : 0);
        device.setUpdateDate(new Date());

        int result = baseDao.updateById(device);

        // 清理相关缓存
        clearDeviceRelatedCache(macAddress);

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchImportDevices(List<String> macAddresses, String registerSource) {
        if (macAddresses == null || macAddresses.isEmpty()) {
            return 0;
        }

        // 获取官方默认智能体
        AgentEntity defaultAgent = getOfficialDefaultAgent();
        if (defaultAgent == null) {
            defaultAgent = createOfficialDefaultAgent();
        }

        int successCount = 0;
        Date now = new Date();

        for (String macAddress : macAddresses) {
            if (StringUtils.isBlank(macAddress)) {
                continue;
            }

            // 验证MAC地址格式
            if (!macAddress.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")) {
                continue;
            }

            // 检查是否已存在
            QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("mac_address", macAddress);
            if (baseDao.selectCount(wrapper) > 0) {
                continue;
            }

            // 创建设备记录
            DeviceEntity device = new DeviceEntity();
            device.setId(UUID.randomUUID().toString().replace("-", ""));
            device.setMacAddress(macAddress);
            device.setAgentId(defaultAgent.getId());
            device.setUserId(null);
            device.setStatus(1); // 默认启用
            device.setRegisterSource(registerSource);
            device.setRegisterTime(now);
            device.setAutoUpdate(1);
            device.setCreateDate(now);
            device.setUpdateDate(now);

            try {
                baseDao.insert(device);
                successCount++;
            } catch (Exception e) {
                log.warn("批量导入设备失败: {}, 错误: {}", macAddress, e.getMessage());
            }
        }

        return successCount;
    }

    /**
     * 清理设备相关的所有缓存
     * 解决缓存一致性问题：自动注册成功后必须清理缓存，确保下次认证时重新查询数据库
     *
     * @param macAddress MAC地址
     */
    private void clearDeviceRelatedCache(String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return;
        }

        try {
            // 1. 清理MAC认证缓存 - 这是关键！
            String macAuthKey = RedisKeys.getMacAuthKey(macAddress);
            redisUtils.delete(macAuthKey);
            log.debug("清理MAC认证缓存: {}", macAuthKey);

            // 2. 清理设备映射缓存
            String deviceMappingKey = "device:mapping:" + macAddress.toLowerCase();
            redisUtils.delete(deviceMappingKey);
            log.debug("清理设备映射缓存: {}", deviceMappingKey);

            // 3. 清理访问频率限制缓存（可选，让其自然过期）
            String rateLimitKey = "mac:rate_limit:" + macAddress;
            redisUtils.delete(rateLimitKey);
            log.debug("清理访问频率缓存: {}", rateLimitKey);

            log.info("已清理设备 {} 的所有相关缓存，确保下次认证时重新查询数据库", macAddress);

        } catch (Exception e) {
            log.warn("清理设备 {} 相关缓存时发生异常: {}", macAddress, e.getMessage());
        }
    }

}