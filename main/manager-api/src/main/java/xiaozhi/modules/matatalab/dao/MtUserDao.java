package xiaozhi.modules.matatalab.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import xiaozhi.modules.matatalab.entity.MtUserEntity;

/**
 * Matatalab用户DAO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface MtUserDao extends BaseMapper<MtUserEntity> {
    
    /**
     * 根据用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户实体
     */
    MtUserEntity getByUsername(@Param("userName") String userName);
    
    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户实体
     */
    MtUserEntity getByEmail(@Param("email") String email);
    
    /**
     * 根据手机号查询用户
     * 
     * @param mobile 手机号
     * @return 用户实体
     */
    MtUserEntity getByMobile(@Param("mobile") String mobile);
}
