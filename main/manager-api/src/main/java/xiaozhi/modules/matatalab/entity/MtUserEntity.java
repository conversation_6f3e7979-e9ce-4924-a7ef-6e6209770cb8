package xiaozhi.modules.matatalab.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Matatalab用户实体 - 对应mt_user表
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@TableName("mt_user")
public class MtUserEntity {
    
    /**
     * 用户ID
     */
    @TableId
    private Long id;
    
    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;
    
    /**
     * 昵称
     */
    @TableField("nickname")
    private String nickname;
    
    /**
     * 邮箱
     */
    @TableField("email")
    private String email;
    
    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;
    
    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 密码盐值
     */
    @TableField("salt")
    private String salt;

    /**
     * 用户类型：0-普通用户，1-学校用户，2-机构用户
     */
    @TableField("type")
    private Integer type;
    
    /**
     * 是否超级管理员：0-否，1-是
     */
    @TableField("super_admin")
    private Integer superAdmin;
    
    /**
     * 状态：0-禁用，1-正常
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
}
