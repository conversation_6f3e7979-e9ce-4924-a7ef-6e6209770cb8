package xiaozhi.modules.matatalab.service;

import org.springframework.stereotype.Service;
import xiaozhi.modules.matatalab.entity.MtUserEntity;
import xiaozhi.modules.sys.dto.SysUserDTO;
import xiaozhi.modules.sys.entity.SysUserEntity;

import java.util.Date;

/**
 * 用户适配服务 - 处理MtUser和SysUser之间的字段映射
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class UserAdapterService {
    
    /**
     * 将MtUserEntity转换为SysUserDTO
     * 
     * @param mtUser MtUser实体
     * @return SysUserDTO
     */
    public SysUserDTO convertToSysUserDTO(MtUserEntity mtUser) {
        if (mtUser == null) {
            return null;
        }
        
        SysUserDTO dto = new SysUserDTO();
        dto.setId(mtUser.getId());
        dto.setUsername(mtUser.getUserName());
        dto.setRealName(mtUser.getNickname() != null ? mtUser.getNickname() : "");
        dto.setEmail(mtUser.getEmail() != null ? mtUser.getEmail() : "");
        dto.setMobile(mtUser.getMobile() != null ? mtUser.getMobile() : "");
        dto.setPassword(mtUser.getPassword());
        dto.setGender(0); // mt_user表没有gender字段，默认为0
        dto.setSuperAdmin(mtUser.getSuperAdmin() != null ? mtUser.getSuperAdmin() : 0);
        dto.setStatus(mtUser.getStatus() != null ? mtUser.getStatus() : 1);
        dto.setCreateDate(mtUser.getCreateTime());
        
        return dto;
    }
    
    /**
     * 将MtUserEntity转换为SysUserEntity
     * 
     * @param mtUser MtUser实体
     * @return SysUserEntity
     */
    public SysUserEntity convertToSysUserEntity(MtUserEntity mtUser) {
        if (mtUser == null) {
            return null;
        }
        
        SysUserEntity entity = new SysUserEntity();
        entity.setId(mtUser.getId());
        entity.setUsername(mtUser.getUserName());
        entity.setPassword(mtUser.getPassword());
        entity.setSuperAdmin(mtUser.getSuperAdmin() != null ? mtUser.getSuperAdmin() : 0);
        entity.setStatus(mtUser.getStatus() != null ? mtUser.getStatus() : 1);
        entity.setUpdater(mtUser.getId()); // 使用用户ID作为更新者
        entity.setUpdateDate(mtUser.getUpdateTime());
        
        return entity;
    }
    
    /**
     * 将SysUserDTO转换为MtUserEntity
     * 
     * @param dto SysUserDTO
     * @return MtUserEntity
     */
    public MtUserEntity convertToMtUserEntity(SysUserDTO dto) {
        if (dto == null) {
            return null;
        }
        
        MtUserEntity entity = new MtUserEntity();
        entity.setId(dto.getId());
        entity.setUserName(dto.getUsername());
        entity.setNickname(dto.getRealName());
        entity.setEmail(dto.getEmail());
        entity.setMobile(dto.getMobile());
        entity.setPassword(dto.getPassword());
        entity.setType(0); // 默认普通用户
        entity.setSuperAdmin(dto.getSuperAdmin());
        entity.setStatus(dto.getStatus());
        entity.setIsDeleted(0); // 默认未删除
        
        Date now = new Date();
        if (dto.getId() == null) {
            // 新用户
            entity.setCreateTime(now);
        }
        entity.setUpdateTime(now);
        
        return entity;
    }
    
    /**
     * 将SysUserEntity转换为MtUserEntity
     * 
     * @param sysUser SysUserEntity
     * @return MtUserEntity
     */
    public MtUserEntity convertToMtUserEntity(SysUserEntity sysUser) {
        if (sysUser == null) {
            return null;
        }
        
        MtUserEntity entity = new MtUserEntity();
        entity.setId(sysUser.getId());
        entity.setUserName(sysUser.getUsername());
        entity.setNickname(""); // SysUserEntity没有realName字段
        entity.setEmail(""); // SysUserEntity没有email字段
        entity.setMobile(""); // SysUserEntity没有mobile字段
        entity.setPassword(sysUser.getPassword());
        entity.setType(0); // 默认普通用户
        entity.setSuperAdmin(sysUser.getSuperAdmin());
        entity.setStatus(sysUser.getStatus());
        entity.setIsDeleted(sysUser.getStatus() == 1 ? 0 : 1); // 状态映射
        
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        
        return entity;
    }
}
