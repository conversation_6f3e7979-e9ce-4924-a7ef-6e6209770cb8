package xiaozhi.modules.matatalab.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import xiaozhi.modules.matatalab.dao.MtUserDao;
import xiaozhi.modules.matatalab.entity.MtUserEntity;
import xiaozhi.modules.matatalab.service.MatatalabJwtUtils;
import xiaozhi.modules.matatalab.service.UserAdapterService;
import xiaozhi.modules.security.entity.SysUserTokenEntity;
import xiaozhi.modules.security.service.ShiroService;
import xiaozhi.modules.sys.entity.SysUserEntity;

import java.util.Date;

/**
 * Matatalab Shiro服务实现 - 使用JWT和mt_user表
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service("shiroService")
@ConditionalOnProperty(name = "xiaozhi.matatalab-user.enabled", havingValue = "true")
@AllArgsConstructor
public class MatatalabShiroServiceImpl implements ShiroService {
    
    private final MtUserDao mtUserDao;
    private final UserAdapterService userAdapterService;
    private final MatatalabJwtUtils jwtUtils;
    
    @Override
    public SysUserEntity getUser(Long userId) {
        MtUserEntity mtUser = mtUserDao.selectById(userId);
        return userAdapterService.convertToSysUserEntity(mtUser);
    }

    @Override
    public SysUserTokenEntity getByToken(String token) {
        if (!jwtUtils.validateToken(token)) {
            return null;
        }

        Long userId = jwtUtils.getUserIdFromToken(token);
        if (userId == null) {
            return null;
        }

        MtUserEntity mtUser = mtUserDao.selectById(userId);
        if (mtUser == null) {
            return null;
        }

        // 创建一个临时的SysUserTokenEntity以通过Shiro验证
        SysUserTokenEntity tokenEntity = new SysUserTokenEntity();
        tokenEntity.setUserId(userId);
        tokenEntity.setToken(token);
        // JWT token的过期由其自身payload控制，这里设置一个较长的过期时间
        Date expireTime = new Date(System.currentTimeMillis() + 3600L * 1000 * 24 * 7); // 7 days
        tokenEntity.setExpireDate(expireTime);
        tokenEntity.setUpdateDate(new Date());

        return tokenEntity;
    }

}
