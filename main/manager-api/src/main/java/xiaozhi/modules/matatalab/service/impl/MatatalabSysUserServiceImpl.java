package xiaozhi.modules.matatalab.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.page.PageData;
import xiaozhi.modules.matatalab.utils.MatatalabPasswordUtils;
import xiaozhi.modules.matatalab.dao.MtUserDao;
import xiaozhi.modules.matatalab.entity.MtUserEntity;
import xiaozhi.modules.matatalab.service.UserAdapterService;
import xiaozhi.modules.sys.dto.AdminPageUserDTO;
import xiaozhi.modules.sys.dto.PasswordDTO;
import xiaozhi.modules.sys.dto.SysUserDTO;
import xiaozhi.modules.sys.entity.SysUserEntity;
import xiaozhi.modules.sys.service.SysUserService;
import xiaozhi.modules.sys.vo.AdminPageUserVO;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Map;

/**
 * Matatalab用户服务实现 - 完全替换原有SysUserService
 * 使用mt_user表替代sys_user表
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service("sysUserService")
@ConditionalOnProperty(name = "xiaozhi.matatalab-user.enabled", havingValue = "true")
@AllArgsConstructor
public class MatatalabSysUserServiceImpl implements SysUserService {
    
    private final MtUserDao mtUserDao;
    private final UserAdapterService userAdapterService;
    
    @Override
    public SysUserDTO getByUserId(Long userId) {
        MtUserEntity entity = mtUserDao.selectById(userId);
        return userAdapterService.convertToSysUserDTO(entity);
    }
    
    @Override
    public SysUserDTO getByUsername(String username) {
        // 支持多种查找方式：用户名、手机号、邮箱
        MtUserEntity entity = findUserByLoginName(username);
        return userAdapterService.convertToSysUserDTO(entity);
    }

    /**
     * 根据登录名查找用户（支持用户名、手机号、邮箱）
     */
    private MtUserEntity findUserByLoginName(String loginName) {
        // 首先尝试用户名查找
        MtUserEntity entity = mtUserDao.getByUsername(loginName);
        if (entity != null) {
            return entity;
        }

        // 然后尝试手机号查找
        entity = mtUserDao.getByMobile(loginName);
        if (entity != null) {
            return entity;
        }

        // 最后尝试邮箱查找
        entity = mtUserDao.getByEmail(loginName);
        return entity;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SysUserDTO dto) {
        MtUserEntity mtUser = userAdapterService.convertToMtUserEntity(dto);
        if (dto.getId() == null) {
            // 新用户，加密密码
            if (dto.getPassword() != null) {
                String salt = MatatalabPasswordUtils.getSalt();
                mtUser.setSalt(salt);
                mtUser.setPassword(MatatalabPasswordUtils.encode(dto.getPassword(), salt));
            }
            mtUserDao.insert(mtUser);
        } else {
            // 更新用户
            mtUserDao.updateById(mtUser);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        // 软删除
        MtUserEntity entity = new MtUserEntity();
        entity.setId(id);
        entity.setIsDeleted(1);
        entity.setUpdateTime(new Date());
        mtUserDao.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(Long userId, PasswordDTO passwordDTO) {
        // 使用matatalab密码加密方式
        String salt = MatatalabPasswordUtils.getSalt();
        MtUserEntity entity = new MtUserEntity();
        entity.setId(userId);
        entity.setSalt(salt);
        entity.setPassword(MatatalabPasswordUtils.encode(passwordDTO.getNewPassword(), salt));
        entity.setUpdateTime(new Date());
        mtUserDao.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePasswordDirectly(Long userId, String password) {
        String salt = MatatalabPasswordUtils.getSalt();
        MtUserEntity entity = new MtUserEntity();
        entity.setId(userId);
        entity.setSalt(salt);
        entity.setPassword(MatatalabPasswordUtils.encode(password, salt));
        entity.setUpdateTime(new Date());
        mtUserDao.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String resetPassword(Long userId) {
        String password = "123456"; // 简化实现，固定密码
        changePasswordDirectly(userId, password);
        return password;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(Integer status, String[] userIds) {
        for (String userId : userIds) {
            MtUserEntity entity = new MtUserEntity();
            entity.setId(Long.parseLong(userId));
            entity.setStatus(status);
            entity.setUpdateTime(new Date());
            mtUserDao.updateById(entity);
        }
    }
    
    @Override
    public PageData<AdminPageUserVO> page(AdminPageUserDTO dto) {
        log.debug("查询用户分页列表，参数：{}", dto);

        // 构建分页参数
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.PAGE, dto.getPage());
        params.put(Constant.LIMIT, dto.getLimit());

        // 计算分页参数
        long curPage = 1;
        long limit = 10;

        if (dto.getPage() != null) {
            curPage = Long.parseLong(dto.getPage());
        }
        if (dto.getLimit() != null) {
            limit = Long.parseLong(dto.getLimit());
        }

        // 计算偏移量
        long offset = (curPage - 1) * limit;

        // 构建查询条件
        QueryWrapper<MtUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", 0); // 只查询未删除的用户

        // 如果有手机号搜索条件，添加模糊查询
        if (StringUtils.isNotBlank(dto.getMobile())) {
            queryWrapper.and(wrapper -> wrapper
                .like("mobile", dto.getMobile())
                .or()
                .like("user_name", dto.getMobile())
                .or()
                .like("email", dto.getMobile())
            );
        }

        // 按ID倒序排列
        queryWrapper.orderByDesc("id");

        // 执行分页查询
        Page<MtUserEntity> page = new Page<>(curPage, limit);
        IPage<MtUserEntity> pageResult = mtUserDao.selectPage(page, queryWrapper);

        // 转换为VO对象
        List<AdminPageUserVO> voList = pageResult.getRecords().stream().map(user -> {
            AdminPageUserVO vo = new AdminPageUserVO();
            vo.setUserid(user.getId().toString());
            vo.setMobile(user.getMobile() != null ? user.getMobile() : user.getUserName());
            vo.setStatus(user.getStatus());
            vo.setCreateDate(user.getCreateTime());

            // 查询设备数量（这里需要注入DeviceService，暂时设为0）
            vo.setDeviceCount("0");

            return vo;
        }).collect(Collectors.toList());

        log.debug("查询到用户数量：{}，总数：{}", voList.size(), pageResult.getTotal());

        return new PageData<>(voList, pageResult.getTotal());
    }

    @Override
    public boolean getAllowUserRegister() {
        // 默认允许用户注册
        return true;
    }
    
    // 实现BaseService接口的必需方法
    @Override
    public Class<SysUserEntity> currentModelClass() {
        return SysUserEntity.class;
    }
    
    @Override
    public boolean insert(SysUserEntity entity) {
        MtUserEntity mtUser = userAdapterService.convertToMtUserEntity(entity);
        return mtUserDao.insert(mtUser) > 0;
    }
    
    @Override
    public boolean insertBatch(Collection<SysUserEntity> entityList) {
        for (SysUserEntity entity : entityList) {
            insert(entity);
        }
        return true;
    }
    
    @Override
    public boolean insertBatch(Collection<SysUserEntity> entityList, int batchSize) {
        return insertBatch(entityList);
    }
    
    @Override
    public boolean updateById(SysUserEntity entity) {
        MtUserEntity mtUser = userAdapterService.convertToMtUserEntity(entity);
        return mtUserDao.updateById(mtUser) > 0;
    }
    
    @Override
    public boolean update(SysUserEntity entity, Wrapper<SysUserEntity> updateWrapper) {
        // 简化实现，忽略wrapper
        return updateById(entity);
    }
    
    @Override
    public boolean updateBatchById(Collection<SysUserEntity> entityList) {
        for (SysUserEntity entity : entityList) {
            updateById(entity);
        }
        return true;
    }
    
    @Override
    public boolean updateBatchById(Collection<SysUserEntity> entityList, int batchSize) {
        return updateBatchById(entityList);
    }
    
    @Override
    public SysUserEntity selectById(Serializable id) {
        MtUserEntity mtUser = mtUserDao.selectById((Long) id);
        return userAdapterService.convertToSysUserEntity(mtUser);
    }
    
    @Override
    public boolean deleteById(Serializable id) {
        // 软删除
        MtUserEntity entity = new MtUserEntity();
        entity.setId((Long) id);
        entity.setIsDeleted(1);
        entity.setUpdateTime(new Date());
        return mtUserDao.updateById(entity) > 0;
    }
    
    @Override
    public boolean deleteBatchIds(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            deleteById(id);
        }
        return true;
    }
}
