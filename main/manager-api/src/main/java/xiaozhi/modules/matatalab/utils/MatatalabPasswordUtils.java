package xiaozhi.modules.matatalab.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.UUID;

/**
 * Matatalab密码工具类 - 兼容matatalab系统的密码加密方式
 * 使用MD5+盐的方式进行密码加密和验证
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class MatatalabPasswordUtils {
    
    private static final String ALGORITHM = "MD5";
    
    /**
     * 匹配密码
     * @param salt 盐
     * @param rawPass 明文密码
     * @param encPass 加密后的密码
     * @return 是否匹配
     */
    public static boolean matches(String salt, String rawPass, String encPass) {
        if (salt == null || rawPass == null || encPass == null) {
            return false;
        }
        String encodedPassword = encode(rawPass, salt);
        return encodedPassword.equals(encPass);
    }
    
    /**
     * 明文密码加密
     * @param rawPass 明文密码
     * @param salt 盐值
     * @return 加密后的密码
     */
    public static String encode(String rawPass, String salt) {
        if (rawPass == null) {
            rawPass = "";
        }
        
        String passwordWithSalt = mergePasswordAndSalt(rawPass, salt);
        
        try {
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            byte[] digest = md.digest(passwordWithSalt.getBytes(StandardCharsets.UTF_8));
            return byteArrayToHexString(digest);
        } catch (Exception e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * 生成盐值
     * @return 20位随机盐值
     */
    public static String getSalt() {
        return UUID.randomUUID().toString().replaceAll("-", "").substring(0, 20);
    }
    
    /**
     * 合并密码和盐值
     * @param password 密码
     * @param salt 盐值
     * @return 合并后的字符串
     */
    private static String mergePasswordAndSalt(String password, String salt) {
        if (password == null) {
            password = "";
        }
        
        if (salt == null || "".equals(salt)) {
            return password;
        } else {
            return password + "{" + salt + "}";
        }
    }
    
    /**
     * 字节数组转十六进制字符串
     * @param byteArray 字节数组
     * @return 十六进制字符串
     */
    private static String byteArrayToHexString(byte[] byteArray) {
        StringBuilder sb = new StringBuilder();
        for (byte b : byteArray) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
    
    /**
     * 验证密码强度（简化版本）
     * @param password 密码
     * @return 是否符合强度要求
     */
    public static boolean isStrongPassword(String password) {
        if (password == null || password.length() < 6) {
            return false;
        }
        // 简化的密码强度验证：至少6位
        return true;
    }
}
