-- 架构优化：智能体表合并 - 第一部分（DDL操作）
-- 执行时间：2025-06-06 18:09:00
-- 说明：
-- 1. 为ai_agent表添加新字段（agent_type, is_active）
-- 2. 创建相关索引
-- 3. 备份ai_default_agent表

-- =============================================================================
-- 第一部分：DDL操作 - 添加字段和索引
-- =============================================================================

-- 1. 添加 agent_type 字段（MySQL 8 兼容语法）
SET @sql = CONCAT('ALTER TABLE ai_agent ADD COLUMN agent_type ENUM(''user'', ''default'') DEFAULT ''user'' COMMENT ''智能体类型''');
SET @column_exists = (SELECT COUNT(*) FROM information_schema.columns
                     WHERE table_schema = DATABASE()
                     AND table_name = 'ai_agent'
                     AND column_name = 'agent_type');

SET @sql = IF(@column_exists = 0, @sql, 'SELECT "agent_type column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 添加 is_active 字段（MySQL 8 兼容语法）
SET @sql = CONCAT('ALTER TABLE ai_agent ADD COLUMN is_active BOOLEAN DEFAULT TRUE COMMENT ''是否激活（仅对默认智能体有效）''');
SET @column_exists = (SELECT COUNT(*) FROM information_schema.columns
                     WHERE table_schema = DATABASE()
                     AND table_name = 'ai_agent'
                     AND column_name = 'is_active');

SET @sql = IF(@column_exists = 0, @sql, 'SELECT "is_active column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 创建索引（MySQL 8 兼容语法）
SET @sql = CONCAT('CREATE INDEX idx_agent_type ON ai_agent(agent_type)');
SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics
                    WHERE table_schema = DATABASE()
                    AND table_name = 'ai_agent'
                    AND index_name = 'idx_agent_type');

SET @sql = IF(@index_exists = 0, @sql, 'SELECT "idx_agent_type index already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE INDEX idx_is_active ON ai_agent(is_active)');
SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics
                    WHERE table_schema = DATABASE()
                    AND table_name = 'ai_agent'
                    AND index_name = 'idx_is_active');

SET @sql = IF(@index_exists = 0, @sql, 'SELECT "idx_is_active index already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 备份原表（如果存在）
-- 注意：ai_default_agent表可能已经不存在，这是正常的

-- =============================================================================
-- 完成
-- =============================================================================
