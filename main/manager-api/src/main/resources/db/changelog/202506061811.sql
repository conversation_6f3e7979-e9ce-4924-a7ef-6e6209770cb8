-- 架构优化：智能体表合并 - 第二部分（DML操作）
-- 执行时间：2025-06-06 18:11:00
-- 说明：
-- 1. 迁移ai_default_agent表数据到ai_agent表
-- 2. 更新现有用户智能体的字段
-- 3. 创建默认智能体
-- 4. 修复模型配置问题
-- 5. 优化设备关联逻辑

-- =============================================================================
-- 第一部分：数据迁移和更新
-- =============================================================================

-- 1. 检查是否需要迁移数据（ai_default_agent_backup表存在的情况下）
-- 注意：如果ai_default_agent表不存在，则跳过迁移步骤
SELECT 'Checking for ai_default_agent_backup table...' as status;

-- 2. 更新现有用户智能体的字段
UPDATE ai_agent
SET agent_type = 'user'
WHERE user_id IS NOT NULL AND (agent_type IS NULL OR agent_type = '');

-- 3. 创建默认智能体（如果不存在）
INSERT IGNORE INTO ai_agent (
    id, agent_code, agent_name,
    asr_model_id, vad_model_id, llm_model_id, vllm_model_id, tts_model_id,
    tts_voice_id, mem_model_id, intent_model_id, system_prompt,
    chat_history_conf, summary_memory, lang_code, language, sort,
    agent_type, is_active, creator, created_at
) VALUES (
    'OFFICIAL_DEFAULT_AGENT',
    'matata',
    'matata',
    'ASR_FunASR',
    'VAD_SileroVAD',
    'LLM_DeepSeekLLM',
    NULL,
    'TTS_EdgeTTS',
    'TTS_EdgeTTS0001',
    'Memory_mem_local_short',
    'Intent_nointent',
    '[角色设定]\n你是一个叫{{assistant_name}}的实物编程机器人，擅长人工智能教育解决方案，支持图形化编程和Python编程。\n请注意，忽略小智这个名字，要像一个人一样说话，请不要回复表情符号、代码和xml标签\n[交互指南]\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话',
    2,
    NULL,
    'zh',
    '中文',
    0,
    'default',  -- 设置为默认智能体
    1,  -- 设置为激活状态
    1,
    NOW()
);

-- 4. 更新设备绑定关系，确保设备关联到正确的智能体
-- 如果设备没有关联智能体，则关联到默认智能体
UPDATE ai_device
SET agent_id = 'OFFICIAL_DEFAULT_AGENT'
WHERE (agent_id IS NULL OR agent_id = '' OR agent_id NOT IN (SELECT id FROM ai_agent))
  AND user_id IS NULL;  -- MAC验证的设备通常user_id为NULL

-- =============================================================================
-- 第二部分：模型配置修复
-- =============================================================================

-- 1. 修复用户智能体中的模型配置（如果有使用不存在的模型ID）
UPDATE ai_agent
SET
    vad_model_id = CASE
        WHEN vad_model_id = 'silero_vad' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'silero_vad')
        THEN 'VAD_SileroVAD'
        ELSE vad_model_id
    END,
    asr_model_id = CASE
        WHEN asr_model_id = 'sense_voice_small' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'sense_voice_small')
        THEN 'ASR_FunASR'
        ELSE asr_model_id
    END,
    llm_model_id = CASE
        WHEN llm_model_id = 'deepseek_chat' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'deepseek_chat')
        THEN 'LLM_DeepSeekLLM'
        ELSE llm_model_id
    END,
    tts_model_id = CASE
        WHEN tts_model_id = 'edge_tts' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'edge_tts')
        THEN 'TTS_EdgeTTS'
        ELSE tts_model_id
    END
WHERE user_id IS NOT NULL  -- 只修复用户智能体
  AND (vad_model_id = 'silero_vad'
       OR asr_model_id = 'sense_voice_small'
       OR llm_model_id = 'deepseek_chat'
       OR tts_model_id = 'edge_tts');

-- 2. 修复智能体模板配置
UPDATE ai_agent_template
SET
    vad_model_id = CASE
        WHEN vad_model_id = 'silero_vad' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'silero_vad')
        THEN 'VAD_SileroVAD'
        ELSE vad_model_id
    END,
    asr_model_id = CASE
        WHEN asr_model_id = 'sense_voice_small' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'sense_voice_small')
        THEN 'ASR_FunASR'
        ELSE asr_model_id
    END,
    llm_model_id = CASE
        WHEN llm_model_id = 'deepseek_chat' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'deepseek_chat')
        THEN 'LLM_DeepSeekLLM'
        ELSE llm_model_id
    END,
    tts_model_id = CASE
        WHEN tts_model_id = 'edge_tts' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'edge_tts')
        THEN 'TTS_EdgeTTS'
        ELSE tts_model_id
    END
WHERE vad_model_id = 'silero_vad'
   OR asr_model_id = 'sense_voice_small'
   OR llm_model_id = 'deepseek_chat'
   OR tts_model_id = 'edge_tts';

-- =============================================================================
-- 第三部分：设备关联逻辑优化（已在第一部分处理）
-- =============================================================================

-- =============================================================================
-- 清理Redis缓存（通过更新时间戳触发缓存失效）
-- =============================================================================

UPDATE ai_model_config
SET update_date = NOW()
WHERE id IN ('TTS_EdgeTTS', 'ASR_FunASR', 'VAD_SileroVAD', 'LLM_DeepSeekLLM', 'LLM_ChatGLMLLM');
