-- 为mt_user表添加xiaozhi系统需要的字段
-- 执行时间：2025-06-12 12:00:00
-- 说明：为matatalab的mt_user表添加xiaozhi系统需要的status、super_admin、is_deleted等字段

-- =============================================================================
-- 第一部分：检查并添加缺失字段
-- =============================================================================

-- 添加status字段（如果不存在）- 复用is_deleted的逻辑，但语义相反
-- status: 1-正常，0-禁用；is_deleted: 0-未删除，1-已删除
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE() AND table_name = 'mt_user' AND column_name = 'status') = 0,
    'ALTER TABLE mt_user ADD COLUMN status TINYINT(1) DEFAULT 1 COMMENT ''状态：0-禁用，1-正常（与is_deleted语义相反但可复用逻辑）'' AFTER type',
    'SELECT "status column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加super_admin字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE() AND table_name = 'mt_user' AND column_name = 'super_admin') = 0,
    'ALTER TABLE mt_user ADD COLUMN super_admin TINYINT(1) DEFAULT 0 COMMENT ''是否超级管理员：0-否，1-是'' AFTER status',
    'SELECT "super_admin column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加is_deleted字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE() AND table_name = 'mt_user' AND column_name = 'is_deleted') = 0,
    'ALTER TABLE mt_user ADD COLUMN is_deleted TINYINT(1) DEFAULT 0 COMMENT ''是否删除：0-未删除，1-已删除'' AFTER super_admin',
    'SELECT "is_deleted column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加create_time字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE() AND table_name = 'mt_user' AND column_name = 'create_time') = 0,
    'ALTER TABLE mt_user ADD COLUMN create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'' AFTER is_deleted',
    'SELECT "create_time column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加update_time字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_schema = DATABASE() AND table_name = 'mt_user' AND column_name = 'update_time') = 0,
    'ALTER TABLE mt_user ADD COLUMN update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'' AFTER create_time',
    'SELECT "update_time column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================================================
-- 第二部分：更新现有数据的默认值（使用动态SQL确保字段存在）
-- =============================================================================

-- 为现有用户设置默认状态值（如果status字段存在）
SET @status_exists = (SELECT COUNT(*) FROM information_schema.columns
                     WHERE table_schema = DATABASE()
                     AND table_name = 'mt_user'
                     AND column_name = 'status');
SET @sql = IF(@status_exists > 0,
    'UPDATE mt_user SET status = 1 WHERE status IS NULL',
    'SELECT "status column does not exist, skipping update" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有用户设置默认super_admin值（如果super_admin字段存在）
SET @super_admin_exists = (SELECT COUNT(*) FROM information_schema.columns
                          WHERE table_schema = DATABASE()
                          AND table_name = 'mt_user'
                          AND column_name = 'super_admin');
SET @sql = IF(@super_admin_exists > 0,
    'UPDATE mt_user SET super_admin = 0 WHERE super_admin IS NULL',
    'SELECT "super_admin column does not exist, skipping update" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有用户设置默认is_deleted值（如果is_deleted字段存在）
SET @is_deleted_exists = (SELECT COUNT(*) FROM information_schema.columns
                         WHERE table_schema = DATABASE()
                         AND table_name = 'mt_user'
                         AND column_name = 'is_deleted');
SET @sql = IF(@is_deleted_exists > 0,
    'UPDATE mt_user SET is_deleted = 0 WHERE is_deleted IS NULL',
    'SELECT "is_deleted column does not exist, skipping update" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有用户设置create_time（如果create_time字段存在）
SET @create_time_exists = (SELECT COUNT(*) FROM information_schema.columns
                          WHERE table_schema = DATABASE()
                          AND table_name = 'mt_user'
                          AND column_name = 'create_time');
SET @sql = IF(@create_time_exists > 0,
    'UPDATE mt_user SET create_time = COALESCE(create_time, NOW()) WHERE create_time IS NULL',
    'SELECT "create_time column does not exist, skipping update" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有用户设置update_time（如果update_time字段存在）
SET @update_time_exists = (SELECT COUNT(*) FROM information_schema.columns
                          WHERE table_schema = DATABASE()
                          AND table_name = 'mt_user'
                          AND column_name = 'update_time');
SET @sql = IF(@update_time_exists > 0,
    'UPDATE mt_user SET update_time = COALESCE(update_time, NOW()) WHERE update_time IS NULL',
    'SELECT "update_time column does not exist, skipping update" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================================================
-- 第三部分：创建测试用户（如果不存在，使用动态SQL确保字段存在）
-- =============================================================================

-- 创建admin测试用户（如果所有字段都存在）
-- SET @all_fields_exist = (
--     SELECT COUNT(*) = 5 FROM information_schema.columns
--     WHERE table_schema = DATABASE()
--     AND table_name = 'mt_user'
--     AND column_name IN ('status', 'super_admin', 'is_deleted', 'create_time', 'update_time')
-- );

-- SET @sql = IF(@all_fields_exist,
--     'INSERT IGNORE INTO mt_user (
--         user_name, nickname, email, mobile, password, type,
--         status, super_admin, is_deleted, create_time, update_time
--     ) VALUES (
--         ''admin'', ''管理员'', ''<EMAIL>'', ''13800138000'',
--         ''$2a$10$N.zmdr9k7uOCQb0VeCp5Le7LXhkxdwHoO7q8H5/iEeZOdyRg/8XLu'',
--         0, 1, 1, 0, NOW(), NOW()
--     )',
--     'SELECT "Required columns do not exist, skipping admin user creation" as message'
-- );
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt;
-- DEALLOCATE PREPARE stmt;

-- =============================================================================
-- 第四部分：创建索引
-- =============================================================================

-- 为status字段创建索引（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.statistics
     WHERE table_schema = DATABASE() AND table_name = 'mt_user' AND index_name = 'idx_mt_user_status') = 0,
    'CREATE INDEX idx_mt_user_status ON mt_user(status)',
    'SELECT "idx_mt_user_status index already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为is_deleted字段创建索引（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.statistics
     WHERE table_schema = DATABASE() AND table_name = 'mt_user' AND index_name = 'idx_mt_user_deleted') = 0,
    'CREATE INDEX idx_mt_user_deleted ON mt_user(is_deleted)',
    'SELECT "idx_mt_user_deleted index already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;


