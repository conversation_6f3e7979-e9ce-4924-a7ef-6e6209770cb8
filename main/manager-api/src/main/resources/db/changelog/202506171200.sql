-- ===============================
-- 添加内置插件工具：handle_movement 和 handle_speaker_or_screen
-- 这些是xiaozhi-server中的内置函数，需要在MCP工具管理中显示
-- ===============================
START TRANSACTION;

-- 1. 移动控制插件
INSERT INTO ai_model_provider (id, model_type, provider_code, name, fields,
                               sort, creator, create_date, updater, update_date)
VALUES ('SYSTEM_PLUGIN_MOVEMENT',
        'Plugin',
        'handle_movement',
        '设备移动控制',
        JSON_ARRAY(),
        100, 0, NOW(), 0, NOW())
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    provider_code = VALUES(provider_code),
    model_type = VALUES(model_type),
    fields = VALUES(fields),
    update_date = NOW();

-- 2. 音量/亮度控制插件
INSERT INTO ai_model_provider (id, model_type, provider_code, name, fields,
                               sort, creator, create_date, updater, update_date)
VALUES ('SYSTEM_PLUGIN_SPEAKER_SCREEN',
        'Plugin',
        'handle_speaker_or_screen',
        '音量/亮度控制',
        JSON_ARRAY(),
        110, 0, NOW(), 0, NOW())
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    provider_code = VALUES(provider_code),
    model_type = VALUES(model_type),
    fields = VALUES(fields),
    update_date = NOW();

COMMIT;
