<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xiaozhi.modules.agent.dao.AgentDao">
    <!-- 获取智能体的设备数量 -->
    <select id="getDeviceCountByAgentId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ai_device WHERE agent_id = #{agentId}
    </select>

    <resultMap id="AgentInfoMap" type="xiaozhi.modules.agent.vo.AgentInfoVO">
        <id column="id"           property="id"/>
        <result column="userId"    property="userId"/>
        <result column="agentCode" property="agentCode"/>
        <result column="agentName" property="agentName"/>
        <result column="asrModelId" property="asrModelId"/>
        <result column="vadModelId" property="vadModelId"/>
        <result column="llmModelId" property="llmModelId"/>
        <result column="ttsModelId" property="ttsModelId"/>
        <result column="ttsVoiceId" property="ttsVoiceId"/>
        <result column="memModelId" property="memModelId"/>
        <result column="intentModelId" property="intentModelId"/>

        <result column="functions" property="functions"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>

        <result column="chatHistoryConf" property="chatHistoryConf"/>
        <result column="systemPrompt" property="systemPrompt"/>
        <result column="summaryMemory" property="summaryMemory"/>
        <result column="langCode" property="langCode"/>
        <result column="language" property="language"/>
        <result column="sort" property="sort"/>
        <result column="agentType" property="agentType"/>
        <result column="isActive" property="isActive"/>
        <result column="creator" property="creator"/>
        <result column="createdAt" property="createdAt"/>
        <result column="updater" property="updater"/>
        <result column="updatedAt" property="updatedAt"/>
    </resultMap>

    <select id="selectAgentInfoById" resultMap="AgentInfoMap">
        SELECT a.id,
               a.user_id           AS userId,
               a.agent_code        AS agentCode,
               a.agent_name        AS agentName,
               a.asr_model_id      AS asrModelId,
               a.vad_model_id      AS vadModelId,
               a.llm_model_id      AS llmModelId,
               a.vllm_model_id     AS vllmModelId,
               a.tts_model_id      AS ttsModelId,
               a.tts_voice_id      AS ttsVoiceId,
               a.mem_model_id      AS memModelId,
               a.intent_model_id   AS intentModelId,
               COALESCE(
                       (SELECT JSON_ARRAYAGG(
                                       JSON_OBJECT(
                                               'id', m.id,
                                               'agentId', m.agent_id,
                                               'pluginId', m.plugin_id,
                                               'paramInfo', m.param_info
                                       )
                               )
                        FROM ai_agent_plugin_mapping m
                        WHERE m.agent_id = a.id),
                       JSON_ARRAY()
               )                   AS functions,
               a.chat_history_conf AS chatHistoryConf,
               a.system_prompt     AS systemPrompt,
               a.summary_memory    AS summaryMemory,
               a.lang_code         AS langCode,
               a.language          AS language,
               a.sort,
               a.agent_type        AS agentType,
               a.is_active         AS isActive,
               a.creator,
               a.created_at        AS createdAt,
               a.updater,
               a.updated_at        AS updatedAt
        FROM ai_agent a
        WHERE a.id = #{agentId}
    </select>
</mapper> 