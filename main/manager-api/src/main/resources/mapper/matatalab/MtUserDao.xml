<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="xiaozhi.modules.matatalab.dao.MtUserDao">

    <resultMap type="xiaozhi.modules.matatalab.entity.MtUserEntity" id="mtUserMap">
        <result property="id" column="id"/>
        <result property="userName" column="user_name"/>
        <result property="nickname" column="nickname"/>
        <result property="email" column="email"/>
        <result property="mobile" column="mobile"/>
        <result property="password" column="password"/>
        <result property="salt" column="salt"/>
        <result property="type" column="type"/>
        <result property="superAdmin" column="super_admin"/>
        <result property="status" column="status"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getByUsername" resultMap="mtUserMap">
        SELECT id,user_name,nickname,email,mobile,password,salt,type,super_admin,status,is_deleted,create_time,update_time
        FROM mt_user
        WHERE user_name = #{userName} AND is_deleted = 0
    </select>

    <select id="getByEmail" resultMap="mtUserMap">
        SELECT id,user_name,nickname,email,mobile,password,salt,type,super_admin,status,is_deleted,create_time,update_time
        FROM mt_user
        WHERE email = #{email} AND is_deleted = 0
    </select>

    <select id="getByMobile" resultMap="mtUserMap">
        SELECT id,user_name,nickname,email,mobile,password,salt,type,super_admin,status,is_deleted,create_time,update_time
        FROM mt_user
        WHERE mobile = #{mobile} AND is_deleted = 0
    </select>

</mapper>
