package xiaozhi.modules.device;

import java.util.HashMap;
import java.util.UUID;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.sys.dto.SysUserDTO;
import xiaozhi.modules.sys.service.SysUserService;

@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
@DisplayName("设备测试")
public class DeviceTest {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private DeviceService deviceService;

    @Test
    public void testSaveUser() {
        SysUserDTO userDTO = new SysUserDTO();
        userDTO.setUsername("test");
        userDTO.setPassword(UUID.randomUUID().toString());
        sysUserService.save(userDTO);
    }

    @Test
    @DisplayName("测试写入设备信息")
    public void testWriteDeviceInfo() {
        log.info("开始测试写入设备信息...");
        // 模拟设备MAC地址
        String macAddress = "00:11:22:33:44:66";
        // 模拟设备验证码
        String deviceCode = "123456";

        HashMap<String, Object> map = new HashMap<>();
        map.put("mac_address", macAddress);
        map.put("activation_code", deviceCode);
        map.put("board", "硬件型号");
        map.put("app_version", "0.3.13");

        String safeDeviceId = macAddress.replace(":", "_").toLowerCase();
        String cacheDeviceKey = String.format("ota:activation:data:%s", safeDeviceId);
        redisUtils.set(cacheDeviceKey, map, 300);

        String redisKey = "ota:activation:code:" + deviceCode;
        log.info("Redis Key: {}", redisKey);

        // 将设备信息写入Redis
        redisUtils.set(redisKey, macAddress, 300);
        log.info("设备信息已写入Redis");

        // 验证是否写入成功
        String savedMacAddress = (String) redisUtils.get(redisKey);
        log.info("从Redis读取的MAC地址: {}", savedMacAddress);

        // 使用断言验证
        Assertions.assertNotNull(savedMacAddress, "从Redis读取的MAC地址不应为空");
        Assertions.assertEquals(macAddress, savedMacAddress, "保存的MAC地址与原始MAC地址不匹配");

        log.info("测试完成");
    }

    @Test
    @DisplayName("测试自动注册设备获取型号信息")
    public void testAutoRegisterWithDeviceInfo() {
        log.info("开始测试自动注册设备获取型号信息...");

        // 模拟设备MAC地址
        String macAddress = "00:11:22:33:44:77";
        String boardType = "ESP32-S3";
        String appVersion = "1.0.5";

        // 先模拟设备上报信息到Redis缓存
        HashMap<String, Object> deviceInfo = new HashMap<>();
        deviceInfo.put("id", macAddress);
        deviceInfo.put("mac_address", macAddress);
        deviceInfo.put("board", boardType);
        deviceInfo.put("app_version", appVersion);
        deviceInfo.put("deviceId", macAddress);

        // 缓存设备信息
        String safeDeviceId = macAddress.replace(":", "_").toLowerCase();
        String cacheKey = String.format("ota:activation:data:%s", safeDeviceId);
        redisUtils.set(cacheKey, deviceInfo, 300);
        log.info("已缓存设备信息到Redis: {}", cacheKey);

        // 执行自动注册（应该从缓存中获取设备信息）
        boolean result = deviceService.bindDeviceToDefaultAgent(macAddress);
        Assertions.assertTrue(result, "自动注册应该成功");

        // 验证设备是否正确创建并包含设备型号信息
        DeviceEntity device = deviceService.getDeviceByMacAddress(macAddress);
        Assertions.assertNotNull(device, "设备应该被创建");
        Assertions.assertEquals(macAddress, device.getMacAddress(), "MAC地址应该匹配");
        Assertions.assertEquals(boardType, device.getBoard(), "设备型号应该从缓存中获取");
        Assertions.assertEquals(appVersion, device.getAppVersion(), "应用版本应该从缓存中获取");
        Assertions.assertEquals("auto", device.getRegisterSource(), "注册来源应该是auto");

        log.info("自动注册测试完成 - 设备型号: {}, 应用版本: {}", device.getBoard(), device.getAppVersion());

        // 清理测试数据
        redisUtils.delete(cacheKey);
        if (device != null) {
            deviceService.delete(device.getId());
        }
    }
}