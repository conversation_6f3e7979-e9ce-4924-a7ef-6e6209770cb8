import os
import sys
import copy
import json
import uuid
import time
import queue
import asyncio
import threading
import traceback
import subprocess
import websockets
from core.utils.util import (
    extract_json_from_string,
    check_vad_update,
    check_asr_update,
    filter_sensitive_info,
)
from typing import Dict, Any
from collections import deque
from core.utils.modules_initialize import (
    initialize_modules,
    initialize_tts,
    initialize_asr,
)
from core.handle.reportHandle import report
from core.providers.tts.default import DefaultTTS
from concurrent.futures import ThreadPoolExecutor
from core.utils.dialogue import Message, Dialogue
from core.providers.asr.dto.dto import InterfaceType
from core.handle.textHandle import handleTextMessage
from core.providers.tools.unified_tool_handler import UnifiedToolHandler
from plugins_func.loadplugins import auto_import_modules
from plugins_func.register import Action, ActionResponse
from core.auth import AuthMiddleware, AuthenticationError
from config.config_loader import get_private_config_from_api
from core.providers.tts.dto.dto import ContentType, TTSMessageDTO, SentenceType
from config.logger import setup_logging, build_module_string, create_connection_logger
from config.manage_api_client import DeviceNotFoundException, DeviceBindException, TokenAuthFailedException, MacAuthFailedException
from core.utils.prompt_manager import PromptManager
from core.utils.voiceprint_provider import VoiceprintProvider
from core.utils import textUtils

TAG = __name__

auto_import_modules("plugins_func.functions")


class TTSException(RuntimeError):
    pass


# 导入MAC认证异常类
try:
    from core.hybrid_auth import UnregisteredMacError
except ImportError:
    # 如果导入失败，定义一个备用的异常类
    class UnregisteredMacError(AuthenticationError):
        """未注册的MAC地址异常"""
        def __init__(self, mac_address):
            self.mac_address = mac_address
            super().__init__(f"未注册的MAC地址: {mac_address}，请前往官网注册您的设备")


class ConnectionHandler:
    def __init__(
        self,
        config: Dict[str, Any],
        _vad,
        _asr,
        _llm,
        _memory,
        _intent,
        server=None,
    ):
        self.common_config = config
        self.config = copy.deepcopy(config)
        self.session_id = str(uuid.uuid4())
        self.logger = setup_logging()
        self.server = server  # 保存server实例的引用

        # 选择认证方式
        self.auth = self._create_auth_middleware()

        self.need_bind = False
        self.bind_code = None
        self.mac_auth_failed = False  # 标记MAC认证失败
        self.read_config_from_api = self.config.get("read_config_from_api", False)

        self.websocket = None
        self.headers = None
        self.device_id = None
        self.client_ip = None
        self.prompt = None
        self.welcome_msg = None
        self.max_output_size = 0
        self.chat_history_conf = 0
        self.audio_format = "opus"

        # Token配额相关配置
        self.device_token_quota = 0      # 设备token配额
        self.account_token_quota = 0     # 账号token配额
        self.enable_token_quota = False  # 是否启用token配额

        # 智能体信息（用于判断配额类型）
        self.agent_user_id = None        # 智能体所属用户ID
        self.agent_id = None             # 智能体ID
        self.agent_name = None           # 智能体名称
        self.is_default_agent = True     # 是否为默认智能体

        # 客户端状态相关
        self.client_abort = False
        self.client_is_speaking = False
        self.client_listen_mode = "auto"

        # 线程任务相关
        self.loop = asyncio.get_event_loop()
        self.stop_event = threading.Event()
        self.executor = ThreadPoolExecutor(max_workers=5)

        # 添加上报线程池
        self.report_queue = queue.Queue()  # TTS上报队列（原有逻辑）
        self.report_thread = None          # TTS上报线程（原有逻辑）

        # Token上报相关（新增独立线程）
        self.token_report_queue = queue.Queue()  # Token使用量上报队列
        self.token_report_thread = None          # Token使用量上报线程

        # Token累积统计（优化上报策略）
        self._accumulated_input_tokens = 0       # 累积的输入Token
        self._accumulated_output_tokens = 0      # 累积的输出Token
        self._current_input_tokens = 0           # 当前对话的输入Token


        # 未来可以通过修改此处，调节asr的上报和tts的上报，目前默认都开启
        self.report_asr_enable = self.read_config_from_api
        self.report_tts_enable = self.read_config_from_api

        # 依赖的组件
        self.vad = None
        self.asr = None
        self.tts = None
        self._asr = _asr
        self._vad = _vad
        self.llm = _llm
        self.memory = _memory
        self.intent = _intent

        # 为每个连接单独管理声纹识别
        self.voiceprint_provider = None

        # vad相关变量
        self.client_audio_buffer = bytearray()
        self.client_have_voice = False
        self.last_activity_time = 0.0  # 统一的活动时间戳（毫秒）
        self.client_voice_stop = False
        self.client_voice_window = deque(maxlen=5)
        self.last_is_voice = False

        # asr相关变量
        # 因为实际部署时可能会用到公共的本地ASR，不能把变量暴露给公共ASR
        # 所以涉及到ASR的变量，需要在这里定义，属于connection的私有变量
        self.asr_audio = []
        self.asr_audio_queue = queue.Queue()

        # llm相关变量
        self.llm_finish_task = True
        self.dialogue = Dialogue()

        # tts相关变量
        self.sentence_id = None

        # iot相关变量
        self.iot_descriptors = {}
        self.func_handler = None

        self.cmd_exit = self.config["exit_commands"]
        self.max_cmd_length = 0
        for cmd in self.cmd_exit:
            if len(cmd) > self.max_cmd_length:
                self.max_cmd_length = len(cmd)

        # 是否在聊天结束后关闭连接
        self.close_after_chat = False
        self.load_function_plugin = False
        self.intent_type = "nointent"

        self.timeout_seconds = (
            int(self.config.get("close_connection_no_voice_time", 120)) + 60
        )  # 在原来第一道关闭的基础上加60秒，进行二道关闭
        self.timeout_task = None

        # {"mcp":true} 表示启用MCP功能
        self.features = None

        # 初始化提示词管理器
        self.prompt_manager = PromptManager(config, self.logger)

    def _create_auth_middleware(self):
        """创建二选一认证中间件：MAC地址认证 或 Token认证"""
        try:
            from core.hybrid_auth import AlternativeAuthMiddleware
            self.logger.bind(tag=TAG).info("使用二选一认证中间件（MAC地址认证 或 Token认证）")
            # 使用common_config（基础配置），而不是self.config（会被设备配置覆盖）
            return AlternativeAuthMiddleware(self.common_config)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"二选一认证中间件初始化失败: {str(e)}")
            self.logger.bind(tag=TAG).info("回退到Token认证")
            from core.auth import AuthMiddleware
            return AuthMiddleware(self.common_config)

    async def handle_connection(self, ws):
        try:
            # 获取并验证headers
            self.headers = dict(ws.request.headers)

            if self.headers.get("device-id", None) is None:
                # 尝试从 URL 的查询参数中获取 device-id
                from urllib.parse import parse_qs, urlparse

                # 从 WebSocket 请求中获取路径
                request_path = ws.request.path
                if not request_path:
                    self.logger.bind(tag=TAG).error("无法获取请求路径")
                    return
                parsed_url = urlparse(request_path)
                query_params = parse_qs(parsed_url.query)
                if "device-id" in query_params:
                    self.headers["device-id"] = query_params["device-id"][0]
                    self.headers["client-id"] = query_params["client-id"][0]
                else:
                    await ws.send("端口正常，如需测试连接，请使用test_page.html")
                    await self.close(ws)
                    return
            # 获取客户端ip地址
            self.client_ip = ws.remote_address[0]
            self.logger.bind(tag=TAG).info(
                f"{self.client_ip} conn - Headers: {self.headers}"
            )

            # 进行认证
            try:
                await self.auth.authenticate(self.headers)
                # 认证通过,继续处理
                self.websocket = ws
                self.device_id = self.headers.get("device-id", None)
				
				# 初始化活动时间戳
                self.last_activity_time = time.time() * 1000
            except UnregisteredMacError as e:
                # MAC认证失败时，不直接关闭连接，而是继续处理以便显示MAC认证失败提示
                self.logger.bind(tag=TAG).warning(f"MAC认证失败，但继续处理以便显示提示: {str(e)}")
                self.websocket = ws
                self.device_id = self.headers.get("device-id", None)
                # 设置MAC认证失败标志
                self.mac_auth_failed = True
                self.need_bind = True
            except AuthenticationError as e:
                # Token认证失败时，不直接关闭连接，而是继续处理以便显示绑定码
                self.logger.bind(tag=TAG).warning(f"Token认证失败，但继续处理以便显示绑定码: {str(e)}")
                self.websocket = ws
                self.device_id = self.headers.get("device-id", None)
                # 设置需要绑定标志，后续会在获取配置时处理绑定码
                self.need_bind = True

            # 启动超时检查任务
            self.timeout_task = asyncio.create_task(self._check_timeout())

            self.welcome_msg = self.config["xiaozhi"]
            self.welcome_msg["session_id"] = self.session_id

            # 获取差异化配置
            self._initialize_private_config()
            # 异步初始化
            self.executor.submit(self._initialize_components)

            try:
                async for message in self.websocket:
                    await self._route_message(message)
            except websockets.exceptions.ConnectionClosed:
                self.logger.bind(tag=TAG).info("客户端断开连接")

        except AuthenticationError as e:
            self.logger.bind(tag=TAG).error(f"Authentication failed: {str(e)}")
            return
        except Exception as e:
            stack_trace = traceback.format_exc()
            self.logger.bind(tag=TAG).error(f"Connection error: {str(e)}-{stack_trace}")
            return
        finally:
            try:
                await self._save_and_close(ws)
            except Exception as final_error:
                self.logger.bind(tag=TAG).error(f"最终清理时出错: {final_error}")
                # 确保即使保存记忆失败，也要关闭连接
                try:
                    await self.close(ws)
                except Exception as close_error:
                    self.logger.bind(tag=TAG).error(
                        f"强制关闭连接时出错: {close_error}"
                    )

    async def _save_and_close(self, ws):
        """保存记忆并关闭连接"""
        try:
            if self.memory:
                # 使用线程池异步保存记忆
                def save_memory_task():
                    try:
                        # 创建新事件循环（避免与主循环冲突）
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(
                            self.memory.save_memory(self.dialogue.dialogue)
                        )
                    except Exception as e:
                        self.logger.bind(tag=TAG).error(f"保存记忆失败: {e}")
                    finally:
                        try:
                            loop.close()
                        except Exception:
                            pass

                # 启动线程保存记忆，不等待完成
                threading.Thread(target=save_memory_task, daemon=True).start()
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"保存记忆失败: {e}")
        finally:
            # 立即关闭连接，不等待记忆保存完成
            try:
                await self.close(ws)
            except Exception as close_error:
                self.logger.bind(tag=TAG).error(
                    f"保存记忆后关闭连接失败: {close_error}"
                )

    async def _route_message(self, message):
        """消息路由"""
        if isinstance(message, str):
            self.last_activity_time = time.time() * 1000
            await handleTextMessage(self, message)
        elif isinstance(message, bytes):
            if self.vad is None:
                return
            if self.asr is None:
                return
            self.asr_audio_queue.put(message)

    async def handle_restart(self, message):
        """处理服务器重启请求"""
        try:

            self.logger.bind(tag=TAG).info("收到服务器重启指令，准备执行...")

            # 发送确认响应
            await self.websocket.send(
                json.dumps(
                    {
                        "type": "server",
                        "status": "success",
                        "message": "服务器重启中...",
                        "content": {"action": "restart"},
                    }
                )
            )

            # 异步执行重启操作
            def restart_server():
                """实际执行重启的方法"""
                time.sleep(1)
                self.logger.bind(tag=TAG).info("执行服务器重启...")
                subprocess.Popen(
                    [sys.executable, "app.py"],
                    stdin=sys.stdin,
                    stdout=sys.stdout,
                    stderr=sys.stderr,
                    start_new_session=True,
                )
                os._exit(0)

            # 使用线程执行重启避免阻塞事件循环
            threading.Thread(target=restart_server, daemon=True).start()

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"重启失败: {str(e)}")
            await self.websocket.send(
                json.dumps(
                    {
                        "type": "server",
                        "status": "error",
                        "message": f"Restart failed: {str(e)}",
                        "content": {"action": "restart"},
                    }
                )
            )

    def _initialize_components(self):
        try:
            self.selected_module_str = build_module_string(
                self.config.get("selected_module", {})
            )
            self.logger = create_connection_logger(self.selected_module_str)
            
            """初始化组件"""
            if self.config.get("prompt") is not None:
                user_prompt = self.config["prompt"]
                # 使用快速提示词进行初始化
                prompt = self.prompt_manager.get_quick_prompt(user_prompt)
                self.change_system_prompt(prompt)
                self.logger.bind(tag=TAG).info(
                    f"快速初始化组件: prompt成功 {prompt[:50]}..."
                )

            """初始化本地组件"""
            if self.vad is None:
                self.vad = self._vad
            if self.asr is None:
                self.asr = self._initialize_asr()
            
            # 初始化声纹识别
            self._initialize_voiceprint()
            
            # 打开语音识别通道
            asyncio.run_coroutine_threadsafe(
                self.asr.open_audio_channels(self), self.loop
            )
            if self.tts is None:
                self.tts = self._initialize_tts()
            # 打开语音合成通道
            asyncio.run_coroutine_threadsafe(
                self.tts.open_audio_channels(self), self.loop
            )

            """加载记忆"""
            self._initialize_memory()
            """加载意图识别"""
            self._initialize_intent()
            """初始化上报线程"""
            self._init_report_threads()
            """更新系统提示词"""
            self._init_prompt_enhancement()

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"实例化组件失败: {e}")

    def _init_prompt_enhancement(self):
        # 更新上下文信息
        self.prompt_manager.update_context_info(self, self.client_ip)
        enhanced_prompt = self.prompt_manager.build_enhanced_prompt(
            self.config["prompt"], self.device_id, self.client_ip
        )
        if enhanced_prompt:
            self.change_system_prompt(enhanced_prompt)
            self.logger.bind(tag=TAG).info("系统提示词已增强更新")

    def _init_report_threads(self):
        """初始化上报线程"""
        if not self.read_config_from_api or self.need_bind:
            return

        # 启动TTS上报线程（依赖chat_history_conf）
        if self.chat_history_conf != 0:
            if self.report_thread is None or not self.report_thread.is_alive():
                self.report_thread = threading.Thread(
                    target=self._report_worker, daemon=True
                )
                self.report_thread.start()
                self.logger.bind(tag=TAG).info("TTS上报线程已启动")

        # 启动Token上报线程（独立于TTS上报，只依赖enable_token_quota）
        if self.enable_token_quota:
            if self.token_report_thread is None or not self.token_report_thread.is_alive():
                self.token_report_thread = threading.Thread(
                    target=self._token_report_worker, daemon=True
                )
                self.token_report_thread.start()
                self.logger.bind(tag=TAG).info("Token上报线程已启动")

    def _initialize_tts(self):
        """初始化TTS"""
        tts = None
        if not self.need_bind:
            tts = initialize_tts(self.config)

        if tts is None:
            tts = DefaultTTS(self.config, delete_audio_file=True)

        return tts

    def _initialize_asr(self):
        """初始化ASR"""
        if self._asr.interface_type == InterfaceType.LOCAL:
            # 如果公共ASR是本地服务，则直接返回
            # 因为本地一个实例ASR，可以被多个连接共享
            asr = self._asr
        else:
            # 如果公共ASR是远程服务，则初始化一个新实例
            # 因为远程ASR，涉及到websocket连接和接收线程，需要每个连接一个实例
            asr = initialize_asr(self.config)

        return asr

    def _initialize_voiceprint(self):
        """为当前连接初始化声纹识别"""
        try:
            voiceprint_config = self.config.get("voiceprint", {})
            if voiceprint_config:
                self.voiceprint_provider = VoiceprintProvider(voiceprint_config)
                self.logger.bind(tag=TAG).info("声纹识别功能已在连接时动态启用")
            else:
                self.logger.bind(tag=TAG).info("声纹识别功能未启用或配置不完整")
        except Exception as e:
            self.logger.bind(tag=TAG).warning(f"声纹识别初始化失败: {str(e)}")

    def _initialize_private_config(self):
        """如果是从配置文件获取，则进行二次实例化"""
        if not self.read_config_from_api:
            return
        """从接口获取差异化的配置进行二次实例化，非全量重新实例化"""
        try:
            begin_time = time.time()
            private_config = get_private_config_from_api(
                self.config,
                self.headers.get("device-id"),
                self.headers.get("client-id", self.headers.get("device-id")),
            )
            private_config["delete_audio"] = bool(self.config.get("delete_audio", True))
            self.logger.bind(tag=TAG).info(
                f"{time.time() - begin_time} 秒，获取差异化配置成功: {json.dumps(filter_sensitive_info(private_config), ensure_ascii=False)}"
            )
        except DeviceNotFoundException as e:
            self.need_bind = True
            private_config = {}
        except DeviceBindException as e:
            self.need_bind = True
            self.bind_code = e.bind_code
            private_config = {}
        except TokenAuthFailedException as e:
            self.need_bind = True
            self.bind_code = e.bind_code
            self.logger.bind(tag=TAG).info(f"Token认证失败，设备未绑定，绑定码: {e.bind_code}")
            private_config = {}
        except MacAuthFailedException as e:
            self.logger.bind(tag=TAG).info(f"MAC认证失败: {str(e)}")
            # MAC认证失败时设置相应标志，确保后续正确处理
            self.mac_auth_failed = True
            self.need_bind = True
            private_config = {}
        except Exception as e:
            self.need_bind = True
            self.logger.bind(tag=TAG).error(f"获取差异化配置失败: {e}")
            private_config = {}

        # 获取设备ID（在整个方法中使用）
        device_id = self.headers.get("device-id", "unknown")

        # 添加调试日志
        self.logger.bind(tag=TAG).info(f"设备 {device_id} need_bind状态: {self.need_bind}, bind_code: {self.bind_code}")

        # 检查是否为MAC认证设备
        is_mac_authenticated = (
            self.headers.get("auth_method") in ["mac_device", "auto_register"] and
            self.headers.get("auth_success") == "true"
        )

        init_llm, init_tts, init_memory, init_intent = (
            False,
            False,
            False,
            False,
        )

        init_vad = check_vad_update(self.common_config, private_config)
        init_asr = check_asr_update(self.common_config, private_config)

        if init_vad:
            self.config["VAD"] = private_config["VAD"]
            self.config["selected_module"]["VAD"] = private_config["selected_module"][
                "VAD"
            ]
        if init_asr:
            self.config["ASR"] = private_config["ASR"]
            self.config["selected_module"]["ASR"] = private_config["selected_module"][
                "ASR"
            ]
        if private_config.get("TTS", None) is not None:
            init_tts = True
            self.config["TTS"] = private_config["TTS"]
            self.config["selected_module"]["TTS"] = private_config["selected_module"][
                "TTS"
            ]
        if private_config.get("LLM", None) is not None:
            init_llm = True
            self.config["LLM"] = private_config["LLM"]
            self.config["selected_module"]["LLM"] = private_config["selected_module"][
                "LLM"
            ]
        if private_config.get("VLLM", None) is not None:
            self.config["VLLM"] = private_config["VLLM"]
            self.config["selected_module"]["VLLM"] = private_config["selected_module"][
                "VLLM"
            ]
        if private_config.get("Memory", None) is not None:
            init_memory = True
            self.config["Memory"] = private_config["Memory"]
            self.config["selected_module"]["Memory"] = private_config[
                "selected_module"
            ]["Memory"]
        if private_config.get("Intent", None) is not None:
            init_intent = True
            self.config["Intent"] = private_config["Intent"]
            model_intent = private_config.get("selected_module", {}).get("Intent", {})
            self.config["selected_module"]["Intent"] = model_intent
            # 加载插件配置
            if model_intent != "Intent_nointent":
                plugin_from_server = private_config.get("plugins", {})
                for plugin, config_str in plugin_from_server.items():
                    plugin_from_server[plugin] = json.loads(config_str)
                self.config["plugins"] = plugin_from_server
                self.config["Intent"][self.config["selected_module"]["Intent"]][
                    "functions"
                ] = plugin_from_server.keys()
        if private_config.get("prompt", None) is not None:
            self.config["prompt"] = private_config["prompt"]
        # 获取声纹信息
        if private_config.get("voiceprint", None) is not None:
            self.config["voiceprint"] = private_config["voiceprint"]
        if private_config.get("summaryMemory", None) is not None:
            self.config["summaryMemory"] = private_config["summaryMemory"]
        if private_config.get("device_max_output_size", None) is not None:
            self.max_output_size = int(private_config["device_max_output_size"])

        # 设置Token配额配置
        if private_config.get("device_token_quota", None) is not None:
            self.device_token_quota = int(private_config["device_token_quota"])

        if private_config.get("account_token_quota", None) is not None:
            self.account_token_quota = int(private_config["account_token_quota"])

        if private_config.get("enable_token_quota", None) is not None:
            self.enable_token_quota = private_config["enable_token_quota"].lower() == "true"

        # 设置智能体信息
        agent_info = private_config.get("agent_info", {})
        agent_type = "default"  # 默认值
        if agent_info:
            self.agent_user_id = agent_info.get("user_id")
            self.agent_id = agent_info.get("agent_id")
            self.agent_name = agent_info.get("agent_name")
            agent_type = agent_info.get("agent_type", "default")

            # 根据智能体类型判断配额类型
            self.is_default_agent = (agent_type == "default")

            # 同步当日Token使用量（如果配置中包含）
            current_device_tokens = private_config.get("current_device_tokens", 0)
            current_account_tokens = private_config.get("current_account_tokens", 0)

            if current_device_tokens > 0 or current_account_tokens > 0:
                from core.utils.token_counter import sync_token_usage_from_config
                sync_token_usage_from_config(
                    self.device_id,
                    str(self.agent_user_id) if self.agent_user_id else None,
                    current_device_tokens,
                    current_account_tokens
                )

            self.logger.bind(tag=TAG).debug(
                f"Token配额配置 - 启用: {self.enable_token_quota}, "
                f"设备配额: {self.device_token_quota}, 账号配额: {self.account_token_quota}, "
                f"智能体: {self.agent_name}({self.agent_id}), 类型: {agent_type}, "
                f"使用配额类型: {'设备级' if self.is_default_agent else '账号级'}, "
                f"当日使用量: 设备{current_device_tokens}, 账号{current_account_tokens}"
            )
        if private_config.get("chat_history_conf", None) is not None:
            self.chat_history_conf = int(private_config["chat_history_conf"])
        if private_config.get("mcp_endpoint", None) is not None:
            self.config["mcp_endpoint"] = private_config["mcp_endpoint"]
        try:
            modules = initialize_modules(
                self.logger,
                private_config,
                init_vad,
                init_asr,
                init_llm,
                init_tts,
                init_memory,
                init_intent,
            )
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"初始化组件失败: {e}")
            modules = {}
        if modules.get("tts", None) is not None:
            self.tts = modules["tts"]
        if modules.get("vad", None) is not None:
            self.vad = modules["vad"]
        if modules.get("asr", None) is not None:
            self.asr = modules["asr"]
        if modules.get("llm", None) is not None:
            self.llm = modules["llm"]
        if modules.get("intent", None) is not None:
            self.intent = modules["intent"]
        if modules.get("memory", None) is not None:
            self.memory = modules["memory"]

    def _initialize_memory(self):
        if self.memory is None:
            return
        """初始化记忆模块"""
        self.memory.init_memory(
            role_id=self.device_id,
            llm=self.llm,
            summary_memory=self.config.get("summaryMemory", None),
            save_to_file=not self.read_config_from_api,
        )

        # 获取记忆总结配置
        memory_config = self.config["Memory"]
        memory_type = self.config["Memory"][self.config["selected_module"]["Memory"]][
            "type"
        ]
        # 如果使用 nomen，直接返回
        if memory_type == "nomem":
            return
        # 使用 mem_local_short 模式
        elif memory_type == "mem_local_short":
            memory_llm_name = memory_config[self.config["selected_module"]["Memory"]][
                "llm"
            ]
            if memory_llm_name and memory_llm_name in self.config["LLM"]:
                # 如果配置了专用LLM，则创建独立的LLM实例
                from core.utils import llm as llm_utils

                memory_llm_config = self.config["LLM"][memory_llm_name]
                memory_llm_type = memory_llm_config.get("type", memory_llm_name)
                memory_llm = llm_utils.create_instance(
                    memory_llm_type, memory_llm_config
                )
                self.logger.bind(tag=TAG).info(
                    f"为记忆总结创建了专用LLM: {memory_llm_name}, 类型: {memory_llm_type}"
                )
                self.memory.set_llm(memory_llm)
            else:
                # 否则使用主LLM
                self.memory.set_llm(self.llm)
                self.logger.bind(tag=TAG).info("使用主LLM作为意图识别模型")

    def _initialize_intent(self):
        if self.intent is None:
            return
        self.intent_type = self.config["Intent"][
            self.config["selected_module"]["Intent"]
        ]["type"]
        if self.intent_type == "function_call" or self.intent_type == "intent_llm":
            self.load_function_plugin = True
        """初始化意图识别模块"""
        # 获取意图识别配置
        intent_config = self.config["Intent"]
        intent_type = self.config["Intent"][self.config["selected_module"]["Intent"]][
            "type"
        ]

        # 如果使用 nointent，直接返回
        if intent_type == "nointent":
            return
        # 使用 intent_llm 模式
        elif intent_type == "intent_llm":
            intent_llm_name = intent_config[self.config["selected_module"]["Intent"]][
                "llm"
            ]

            if intent_llm_name and intent_llm_name in self.config["LLM"]:
                # 如果配置了专用LLM，则创建独立的LLM实例
                from core.utils import llm as llm_utils

                intent_llm_config = self.config["LLM"][intent_llm_name]
                intent_llm_type = intent_llm_config.get("type", intent_llm_name)
                intent_llm = llm_utils.create_instance(
                    intent_llm_type, intent_llm_config
                )
                self.logger.bind(tag=TAG).info(
                    f"为意图识别创建了专用LLM: {intent_llm_name}, 类型: {intent_llm_type}"
                )
                self.intent.set_llm(intent_llm)
            else:
                # 否则使用主LLM
                self.intent.set_llm(self.llm)
                self.logger.bind(tag=TAG).info("使用主LLM作为意图识别模型")

        """加载统一工具处理器"""
        self.func_handler = UnifiedToolHandler(self)

        # 异步初始化工具处理器
        if hasattr(self, "loop") and self.loop:
            asyncio.run_coroutine_threadsafe(self.func_handler._initialize(), self.loop)

    def change_system_prompt(self, prompt):
        self.prompt = prompt
        # 更新系统prompt至上下文
        self.dialogue.update_system_message(self.prompt)

    def chat(self, query, tool_call=False, depth=0):
        self.logger.bind(tag=TAG).info(f"大模型收到用户消息: {query}")
        self.llm_finish_task = False

        if not tool_call:
            self.dialogue.put(Message(role="user", content=query))

        # 为最顶层时新建会话ID和发送FIRST请求
        if depth == 0:
            self.sentence_id = str(uuid.uuid4().hex)
            self.tts.tts_text_queue.put(
                TTSMessageDTO(
                    sentence_id=self.sentence_id,
                    sentence_type=SentenceType.FIRST,
                    content_type=ContentType.ACTION,
                )
            )

        # Define intent functions
        functions = None
        if self.intent_type == "function_call" and hasattr(self, "func_handler"):
            functions = self.func_handler.get_functions()
        response_message = []

        try:
            # 使用带记忆的对话
            memory_str = None
            if self.memory is not None:
                future = asyncio.run_coroutine_threadsafe(
                    self.memory.query_memory(query), self.loop
                )
                memory_str = future.result()

            if self.intent_type == "function_call" and functions is not None:
                # 使用支持functions的streaming接口
                llm_responses = self.llm.response_with_functions(
                    self.session_id,
                    self.dialogue.get_llm_dialogue_with_memory(
                        memory_str, self.config.get("voiceprint", {})
                    ),
                    functions=functions,
                )
            else:
                llm_responses = self.llm.response(
                    self.session_id,
                    self.dialogue.get_llm_dialogue_with_memory(
                        memory_str, self.config.get("voiceprint", {})
                    ),
                )
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"LLM 处理出错 {query}: {e}")
            return None

        # 处理流式响应
        tool_call_flag = False
        function_name = None
        function_id = None
        function_arguments = ""
        content_arguments = ""
        self.client_abort = False
        emotion_flag = True
        for response in llm_responses:
            if self.client_abort:
                break
            if self.intent_type == "function_call" and functions is not None:
                content, tools_call = response
                if "content" in response:
                    content = response["content"]
                    tools_call = None
                if content is not None and len(content) > 0:
                    content_arguments += content

                if not tool_call_flag and content_arguments.startswith("<tool_call>"):
                    # print("content_arguments", content_arguments)
                    tool_call_flag = True

                if tools_call is not None and len(tools_call) > 0:
                    tool_call_flag = True
                    if tools_call[0].id is not None:
                        function_id = tools_call[0].id
                    if tools_call[0].function.name is not None:
                        function_name = tools_call[0].function.name
                    if tools_call[0].function.arguments is not None:
                        function_arguments += tools_call[0].function.arguments
            else:
                content = response

            # 在llm回复中获取情绪表情，一轮对话只在开头获取一次
            if emotion_flag and content is not None and content.strip():
                asyncio.run_coroutine_threadsafe(
                    textUtils.get_emotion(self, content),
                    self.loop,
                )
                emotion_flag = False

            if content is not None and len(content) > 0:
                if not tool_call_flag:
                    response_message.append(content)
                    self.tts.tts_text_queue.put(
                        TTSMessageDTO(
                            sentence_id=self.sentence_id,
                            sentence_type=SentenceType.MIDDLE,
                            content_type=ContentType.TEXT,
                            content_detail=content,
                        )
                    )
        # 处理function call
        if tool_call_flag:
            bHasError = False
            if function_id is None:
                a = extract_json_from_string(content_arguments)
                if a is not None:
                    try:
                        content_arguments_json = json.loads(a)
                        function_name = content_arguments_json["name"]
                        function_arguments = json.dumps(
                            content_arguments_json["arguments"], ensure_ascii=False
                        )
                        function_id = str(uuid.uuid4().hex)
                    except Exception as e:
                        bHasError = True
                        response_message.append(a)
                else:
                    bHasError = True
                    response_message.append(content_arguments)
                if bHasError:
                    self.logger.bind(tag=TAG).error(
                        f"function call error: {content_arguments}"
                    )
            if not bHasError:
                # 如需要大模型先处理一轮，添加相关处理后的日志情况
                if len(response_message) > 0:
                    self.dialogue.put(
                        Message(role="assistant", content="".join(response_message))
                    )
                response_message.clear()
                self.logger.bind(tag=TAG).debug(
                    f"function_name={function_name}, function_id={function_id}, function_arguments={function_arguments}"
                )
                function_call_data = {
                    "name": function_name,
                    "id": function_id,
                    "arguments": function_arguments,
                }

                # 使用统一工具处理器处理所有工具调用
                result = asyncio.run_coroutine_threadsafe(
                    self.func_handler.handle_llm_function_call(
                        self, function_call_data
                    ),
                    self.loop,
                ).result()
                self._handle_function_result(result, function_call_data, depth=depth)

        # 存储对话内容
        if len(response_message) > 0:
            self.dialogue.put(
                Message(role="assistant", content="".join(response_message))
            )
        if depth == 0:
            self.tts.tts_text_queue.put(
                TTSMessageDTO(
                    sentence_id=self.sentence_id,
                    sentence_type=SentenceType.LAST,
                    content_type=ContentType.ACTION,
                )
            )
        self.llm_finish_task = True
        # 使用lambda延迟计算，只有在DEBUG级别时才执行get_llm_dialogue()
        self.logger.bind(tag=TAG).debug(
            lambda: json.dumps(
                self.dialogue.get_llm_dialogue(), indent=4, ensure_ascii=False
            )
        )

        return True

    def _record_and_report_token_usage(self, input_text: str, output_text: str):
        """记录并上报Token使用量"""
        try:
            # 计算Token使用量
            input_tokens = self._estimate_input_tokens(input_text)
            output_tokens = self._calculate_output_tokens(output_text)
            total_tokens = input_tokens + output_tokens

            # 记录到本地缓存
            self._record_token_usage_locally(input_tokens, output_tokens, total_tokens)

            # 异步上报到Java后端
            self._enqueue_token_report(input_tokens, output_tokens, total_tokens)

            self.logger.bind(tag=TAG).debug(
                f"Token使用量统计完成 - 输入: {input_tokens}, 输出: {output_tokens}, 总计: {total_tokens}"
            )

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Token使用量统计失败: {e}")

    def _calculate_output_tokens(self, output_text: str) -> int:
        """计算输出token数"""
        from core.utils.token_counter import estimate_tokens_from_text

        # 尝试从LLM响应中获取实际token数（如果支持）
        if hasattr(self.llm, 'last_usage') and self.llm.last_usage:
            actual_tokens = getattr(self.llm.last_usage, 'completion_tokens', 0)
            if actual_tokens > 0:
                return actual_tokens

        # 否则根据模型类型估算
        model_type = getattr(self.llm, 'model_type', 'general')
        return estimate_tokens_from_text(output_text, model_type)

    def _estimate_input_tokens(self, input_text: str) -> int:
        """估算输入token数（仅计算当前输入，避免重复计算历史对话）"""
        from core.utils.token_counter import estimate_tokens_from_text

        # 优先尝试从LLM获取实际输入token数
        if hasattr(self.llm, 'last_usage') and self.llm.last_usage:
            actual_tokens = getattr(self.llm.last_usage, 'prompt_tokens', 0)
            if actual_tokens > 0:
                return actual_tokens

        # 否则仅估算当前输入文本的token数（不包含历史对话，避免重复计算）
        model_type = getattr(self.llm, 'model_type', 'general')
        return estimate_tokens_from_text(input_text, model_type)

    def _record_token_usage_locally(self, input_tokens: int, output_tokens: int, total_tokens: int):
        """记录token使用量到本地缓存"""
        from core.utils.token_counter import add_device_tokens, add_account_tokens, check_device_token_limit, check_account_token_limit

        if self.is_default_agent:
            # 默认智能体（ai_default_agent表）：使用设备级配额
            add_device_tokens(self.device_id, total_tokens)
            self.logger.bind(tag=TAG).debug(f"记录设备级token使用量: {self.device_id} +{total_tokens}")

            # 检查是否达到限额，如果达到则标记需要强制上报
            if self.device_token_quota > 0:
                is_exceeded = check_device_token_limit(self.device_id, self.device_token_quota)
                if is_exceeded:
                    self._force_token_report = True
                    self.logger.bind(tag=TAG).warning(f"设备级配额达到限制，将强制上报: {self.device_id}")
        else:
            # 用户智能体（ai_agent表）：使用账号级配额
            if self.agent_user_id:
                add_account_tokens(str(self.agent_user_id), total_tokens)
                self.logger.bind(tag=TAG).debug(f"记录账号级token使用量: 用户{self.agent_user_id} +{total_tokens}")

                # 检查是否达到限额，如果达到则标记需要强制上报
                if self.account_token_quota > 0:
                    is_exceeded = check_account_token_limit(str(self.agent_user_id), self.account_token_quota)
                    if is_exceeded:
                        self._force_token_report = True
                        self.logger.bind(tag=TAG).warning(f"账号级配额达到限制，将强制上报: 用户{self.agent_user_id}")
            else:
                self.logger.bind(tag=TAG).warning(f"用户智能体缺少user_id，无法记录账号级配额: {self.agent_id}")

    def _enqueue_token_report(self, input_tokens: int, output_tokens: int, total_tokens: int):
        """将token使用量加入上报队列"""
        if not self.read_config_from_api:
            return

        # 检查是否需要强制上报（达到限额时）
        force_report = getattr(self, '_force_token_report', False)
        if force_report:
            # 重置强制上报标记
            self._force_token_report = False

        token_data = {
            "type": "token_usage",
            "device_mac": self.device_id,
            "user_id": self.agent_user_id,
            "agent_id": self.agent_id,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "total_tokens": total_tokens,
            "model_name": getattr(self.llm, 'model_name', 'unknown'),
            "is_default_agent": self.is_default_agent,
            "force_report": force_report  # 标记是否为强制上报
        }

        # 使用独立的Token上报队列
        self.token_report_queue.put(("token_usage", token_data, time.time()))

    def _record_input_tokens(self, input_text: str):
        """记录输入Token使用量（ASR阶段）"""
        try:
            # 计算输入Token使用量
            input_tokens = self._estimate_input_tokens(input_text)

            # 只记录输入Token到本地缓存，输出Token在TTS阶段记录
            self._record_token_usage_locally(input_tokens, 0, input_tokens)

            # 暂存输入Token数，供TTS阶段使用
            self._current_input_tokens = input_tokens

            self.logger.bind(tag=TAG).info(
                f"ASR阶段Token统计 - 输入文本: '{input_text[:50]}...', 输入Token: {input_tokens}"
            )

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"输入Token统计失败: {e}")

    def _record_tts_output_tokens(self, text: str):
        """在TTS阶段记录输出Token使用量（并行于字数统计）"""
        try:
            # 计算输出Token使用量
            output_tokens = self._calculate_output_tokens(text)

            # 获取之前记录的输入Token数（如果有的话）
            input_tokens = getattr(self, '_current_input_tokens', 0)
            total_tokens = input_tokens + output_tokens

            # 只记录输出Token到本地缓存（输入Token已在ASR阶段记录）
            self._record_token_usage_locally(0, output_tokens, output_tokens)

            # 累积Token使用量，不立即上报（优化上报策略）
            self._accumulate_token_usage(input_tokens, output_tokens, total_tokens)

            self.logger.bind(tag=TAG).info(
                f"TTS阶段Token统计 - 输出文本: '{text[:50]}...', 输出Token: {output_tokens}, 总计: {total_tokens}"
            )

            # 清除暂存的输入Token数
            self._current_input_tokens = 0

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"TTS Token统计失败: {e}")

    def _accumulate_token_usage(self, input_tokens: int, output_tokens: int, total_tokens: int):
        """累积Token使用量，优化上报策略"""
        self._accumulated_input_tokens += input_tokens
        self._accumulated_output_tokens += output_tokens

        # 检查是否需要强制上报（达到限额时）
        force_report = getattr(self, '_force_token_report', False)
        if force_report:
            self._force_report_accumulated_tokens()
            self._force_token_report = False

    def _force_report_accumulated_tokens(self):
        """强制上报累积的Token使用量（达到限额时）"""
        if self._accumulated_input_tokens > 0 or self._accumulated_output_tokens > 0:
            total_tokens = self._accumulated_input_tokens + self._accumulated_output_tokens

            # 打印本地缓存数据
            self._print_local_cache_data("强制上报前")

            self._enqueue_token_report(
                self._accumulated_input_tokens,
                self._accumulated_output_tokens,
                total_tokens
            )
            self.logger.bind(tag=TAG).info(
                f"强制上报累积Token - 输入: {self._accumulated_input_tokens}, "
                f"输出: {self._accumulated_output_tokens}, 总计: {total_tokens}"
            )
            # 清空累积计数
            self._accumulated_input_tokens = 0
            self._accumulated_output_tokens = 0

    def _report_accumulated_tokens_on_disconnect(self):
        """客户端断开时上报累积的Token使用量"""
        if self._accumulated_input_tokens > 0 or self._accumulated_output_tokens > 0:
            total_tokens = self._accumulated_input_tokens + self._accumulated_output_tokens

            # 打印本地缓存数据
            self._print_local_cache_data("断开连接上报前")

            self._enqueue_token_report(
                self._accumulated_input_tokens,
                self._accumulated_output_tokens,
                total_tokens
            )
            self.logger.bind(tag=TAG).info(
                f"断开连接时上报累积Token - 输入: {self._accumulated_input_tokens}, "
                f"输出: {self._accumulated_output_tokens}, 总计: {total_tokens}"
            )
            # 清空累积计数
            self._accumulated_input_tokens = 0
            self._accumulated_output_tokens = 0

    def _print_local_cache_data(self, context: str):
        """打印本地缓存数据"""
        try:
            from core.utils.token_counter import get_device_tokens, get_account_tokens

            # 获取本地缓存数据
            if self.is_default_agent:
                device_tokens = get_device_tokens(self.device_id)
                self.logger.bind(tag=TAG).info(
                    f"{context} - 本地缓存数据: 设备{self.device_id} = {device_tokens}个Token"
                )
            else:
                if self.agent_user_id:
                    account_tokens = get_account_tokens(str(self.agent_user_id))
                    self.logger.bind(tag=TAG).info(
                        f"{context} - 本地缓存数据: 用户{self.agent_user_id} = {account_tokens}个Token"
                    )

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"打印本地缓存数据失败: {e}")

    def _handle_mcp_tool_call(self, function_call_data):
        function_arguments = function_call_data["arguments"]
        function_name = function_call_data["name"]
        try:
            args_dict = function_arguments
            if isinstance(function_arguments, str):
                try:
                    args_dict = json.loads(function_arguments)
                except json.JSONDecodeError:
                    self.logger.bind(tag=TAG).error(
                        f"无法解析 function_arguments: {function_arguments}"
                    )
                    return ActionResponse(
                        action=Action.REQLLM, result="参数解析失败", response=""
                    )

            tool_result = asyncio.run_coroutine_threadsafe(
                self.mcp_manager.execute_tool(function_name, args_dict), self.loop
            ).result()
            # meta=None content=[TextContent(type='text', text='北京当前天气:\n温度: 21°C\n天气: 晴\n湿度: 6%\n风向: 西北 风\n风力等级: 5级', annotations=None)] isError=False
            content_text = ""
            if tool_result is not None and tool_result.content is not None:
                for content in tool_result.content:
                    content_type = content.type
                    if content_type == "text":
                        content_text = content.text
                    elif content_type == "image":
                        pass

            if len(content_text) > 0:
                return ActionResponse(
                    action=Action.REQLLM, result=content_text, response=""
                )

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"MCP工具调用错误: {e}")
            return ActionResponse(
                action=Action.REQLLM, result="工具调用出错", response=""
            )

        return ActionResponse(action=Action.REQLLM, result="工具调用出错", response="")

    def _handle_function_result(self, result, function_call_data, depth):
        if result.action == Action.RESPONSE:  # 直接回复前端
            text = result.response
            self.tts.tts_one_sentence(self, ContentType.TEXT, content_detail=text)
            self.dialogue.put(Message(role="assistant", content=text))
        elif result.action == Action.REQLLM:  # 调用函数后再请求llm生成回复
            text = result.result
            if text is not None and len(text) > 0:
                function_id = function_call_data["id"]
                function_name = function_call_data["name"]
                function_arguments = function_call_data["arguments"]
                self.dialogue.put(
                    Message(
                        role="assistant",
                        tool_calls=[
                            {
                                "id": function_id,
                                "function": {
                                    "arguments": function_arguments,
                                    "name": function_name,
                                },
                                "type": "function",
                                "index": 0,
                            }
                        ],
                    )
                )

                self.dialogue.put(
                    Message(
                        role="tool",
                        tool_call_id=(
                            str(uuid.uuid4()) if function_id is None else function_id
                        ),
                        content=text,
                    )
                )
                self.chat(text, tool_call=True, depth=depth + 1)
        elif result.action == Action.NOTFOUND or result.action == Action.ERROR:
            text = result.response if result.response else result.result
            self.tts.tts_one_sentence(self, ContentType.TEXT, content_detail=text)
            self.dialogue.put(Message(role="assistant", content=text))
        else:
            pass

    def _report_worker(self):
        """聊天记录上报工作线程"""
        while not self.stop_event.is_set():
            try:
                # 从队列获取数据，设置超时以便定期检查停止事件
                item = self.report_queue.get(timeout=1)
                if item is None:  # 检测毒丸对象
                    break
                try:
                    # 检查线程池状态
                    if self.executor is None:
                        continue
                    # 提交任务到线程池
                    self.executor.submit(
                        self._process_report, *item
                    )
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"聊天记录上报线程异常: {e}")
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"聊天记录上报工作线程异常: {e}")

        self.logger.bind(tag=TAG).info("聊天记录上报线程已退出")

    def _process_report(self, type, text, audio_data, report_time):
        """处理上报任务"""
        try:
            # 执行上报（传入二进制数据）
            report(self, type, text, audio_data, report_time)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"上报处理异常: {e}")
        finally:
            # 标记任务完成
            self.report_queue.task_done()

    def _process_token_report(self, token_data, report_time):
        """处理Token使用量上报"""
        try:
            from config.manage_api_client import report_token_usage

            force_report = token_data.get("force_report", False)

            # 打印上报的Token数据和本地缓存数据
            self.logger.bind(tag=TAG).info(
                f"开始上报Token - 设备: {token_data['device_mac']}, "
                f"输入: {token_data['input_tokens']}, 输出: {token_data['output_tokens']}, "
                f"总计: {token_data['total_tokens']}, 强制上报: {force_report}"
            )
            self._print_local_cache_data("Token上报时")

            # 调用Java端API上报token使用量
            result = report_token_usage(
                device_mac=token_data["device_mac"],
                input_tokens=token_data["input_tokens"],
                output_tokens=token_data["output_tokens"],
                model_name=token_data["model_name"],
                user_id=token_data.get("user_id"),
                agent_id=token_data.get("agent_id")
            )

            if result:
                log_level = "warning" if force_report else "debug"
                log_msg = f"Token使用量上报成功: {token_data['total_tokens']} tokens"
                if force_report:
                    log_msg += " (配额限制触发的强制上报)"

                if log_level == "warning":
                    self.logger.bind(tag=TAG).warning(log_msg)
                else:
                    self.logger.bind(tag=TAG).debug(log_msg)
            else:
                self.logger.bind(tag=TAG).warning(f"Token使用量上报失败: {token_data}")

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Token使用量上报异常: {e}")

    def _token_report_worker(self):
        """Token使用量上报工作线程（独立于TTS上报）"""
        self.logger.bind(tag=TAG).info("Token上报工作线程已启动")
        while not self.stop_event.is_set():
            try:
                # 从Token上报队列获取数据，设置超时以便定期检查停止事件
                type, token_data, report_time = self.token_report_queue.get(timeout=1)
                if token_data is None:  # 检测毒丸对象
                    break
                try:
                    # 检查线程池状态
                    if self.executor is None:
                        continue
                    # 提交Token上报任务到线程池
                    self.executor.submit(
                        self._process_token_report, token_data, report_time
                    )
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"Token上报线程异常: {e}")
                finally:
                    self.token_report_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"Token上报工作线程异常: {e}")

        self.logger.bind(tag=TAG).info("Token上报线程已退出")

    def clearSpeakStatus(self):
        self.client_is_speaking = False
        self.logger.bind(tag=TAG).debug(f"清除服务端讲话状态")

    async def close(self, ws=None):
        """资源清理方法"""
        try:
            # 取消超时任务
            if self.timeout_task and not self.timeout_task.done():
                self.timeout_task.cancel()
                try:
                    await self.timeout_task
                except asyncio.CancelledError:
                    pass
                self.timeout_task = None

            # 清理工具处理器资源
            if hasattr(self, "func_handler") and self.func_handler:
                try:
                    await self.func_handler.cleanup()
                except Exception as cleanup_error:
                    self.logger.bind(tag=TAG).error(
                        f"清理工具处理器时出错: {cleanup_error}"
                    )

            # 触发停止事件
            if self.stop_event:
                self.stop_event.set()

            # 上报累积的Token使用量
            if self.enable_token_quota:
                self._report_accumulated_tokens_on_disconnect()

            # 清空任务队列
            self.clear_queues()

            # 关闭WebSocket连接
            try:
                if ws:
                    # 安全地检查WebSocket状态并关闭
                    try:
                        if hasattr(ws, "closed") and not ws.closed:
                            await ws.close()
                        elif hasattr(ws, "state") and ws.state.name != "CLOSED":
                            await ws.close()
                        else:
                            # 如果没有closed属性，直接尝试关闭
                            await ws.close()
                    except Exception:
                        # 如果关闭失败，忽略错误
                        pass
                elif self.websocket:
                    try:
                        if (
                            hasattr(self.websocket, "closed")
                            and not self.websocket.closed
                        ):
                            await self.websocket.close()
                        elif (
                            hasattr(self.websocket, "state")
                            and self.websocket.state.name != "CLOSED"
                        ):
                            await self.websocket.close()
                        else:
                            # 如果没有closed属性，直接尝试关闭
                            await self.websocket.close()
                    except Exception:
                        # 如果关闭失败，忽略错误
                        pass
            except Exception as ws_error:
                self.logger.bind(tag=TAG).error(f"关闭WebSocket连接时出错: {ws_error}")

            if self.tts:
                await self.tts.close()

            # 最后关闭线程池（避免阻塞）
            if self.executor:
                try:
                    self.executor.shutdown(wait=False)
                except Exception as executor_error:
                    self.logger.bind(tag=TAG).error(
                        f"关闭线程池时出错: {executor_error}"
                    )
                self.executor = None

            self.logger.bind(tag=TAG).info("连接资源已释放")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"关闭连接时出错: {e}")
        finally:
            # 确保停止事件被设置
            if self.stop_event:
                self.stop_event.set()

    def clear_queues(self):
        """清空所有任务队列"""
        if self.tts:
            self.logger.bind(tag=TAG).debug(
                f"开始清理: TTS队列大小={self.tts.tts_text_queue.qsize()}, 音频队列大小={self.tts.tts_audio_queue.qsize()}"
            )

            # 使用非阻塞方式清空队列
            for q in [
                self.tts.tts_text_queue,
                self.tts.tts_audio_queue,
                self.report_queue,
            ]:
                if not q:
                    continue
                while True:
                    try:
                        q.get_nowait()
                    except queue.Empty:
                        break

            self.logger.bind(tag=TAG).debug(
                f"清理结束: TTS队列大小={self.tts.tts_text_queue.qsize()}, 音频队列大小={self.tts.tts_audio_queue.qsize()}"
            )

    def reset_vad_states(self):
        self.client_audio_buffer = bytearray()
        self.client_have_voice = False
        self.client_voice_stop = False
        self.logger.bind(tag=TAG).debug("VAD states reset.")

    def chat_and_close(self, text):
        """Chat with the user and then close the connection"""
        try:
            # Use the existing chat method
            self.chat(text)

            # After chat is complete, close the connection
            self.close_after_chat = True
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Chat and close error: {str(e)}")

    async def _check_timeout(self):
        """检查连接超时"""
        try:
            while not self.stop_event.is_set():
                # 检查是否超时（只有在时间戳已初始化的情况下）
                if self.last_activity_time > 0.0:
                    current_time = time.time() * 1000
                    if (
                        current_time - self.last_activity_time
                        > self.timeout_seconds * 1000
                    ):
                        if not self.stop_event.is_set():
                            self.logger.bind(tag=TAG).info("连接超时，准备关闭")
                            # 设置停止事件，防止重复处理
                            self.stop_event.set()
                            # 使用 try-except 包装关闭操作，确保不会因为异常而阻塞
                            try:
                                await self.close(self.websocket)
                            except Exception as close_error:
                                self.logger.bind(tag=TAG).error(
                                    f"超时关闭连接时出错: {close_error}"
                                )
                        break
                # 每10秒检查一次，避免过于频繁
                await asyncio.sleep(10)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"超时检查任务出错: {e}")
        finally:
            self.logger.bind(tag=TAG).info("超时检查任务已退出")
