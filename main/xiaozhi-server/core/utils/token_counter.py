"""
Token使用量统计模块 - 每日重置
简化版本，删除无用接口，实现每日0点重置机制

配额类型说明：
- 设备级配额：默认智能体按MAC地址统计，每日0点重置
- 账号级配额：用户智能体按用户ID统计，每日0点重置
"""
import datetime
import json
import os
from typing import Dict, Optional
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

# 设备级token使用量缓存 {device_id: {'tokens': int, 'date': date}}
_device_tokens: Dict[str, Dict] = {}

# 账号级token使用量缓存 {user_id: {'tokens': int, 'date': date}}
_account_tokens: Dict[str, Dict] = {}

# 初始化标志
_initialized = False

# 数据持久化文件路径
def _get_data_dir():
    """获取数据目录路径（与配置文件相同的data目录）"""
    from config.config_loader import get_project_dir
    return get_project_dir() + "data"

def _get_token_usage_file(date: Optional[datetime.date] = None):
    """获取Token使用量文件路径（按日期分文件）"""
    if date is None:
        date = datetime.datetime.now().date()
    filename = f"token_usage_{date.strftime('%Y%m%d')}.json"
    return os.path.join(_get_data_dir(), filename)


def _ensure_data_dir():
    """确保data目录存在"""
    data_dir = _get_data_dir()
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

def _save_token_usage_to_file():
    """保存Token使用量到文件（按日期分文件）"""
    try:
        _ensure_data_dir()

        current_date = datetime.datetime.now().date()
        token_file = _get_token_usage_file(current_date)

        data = {
            "date": current_date.isoformat(),
            "devices": {},
            "accounts": {}
        }

        # 保存设备Token使用量（只保存当天的）
        for device_id, info in _device_tokens.items():
            if info["date"] == current_date:
                data["devices"][device_id] = info["tokens"]

        # 保存账号Token使用量（只保存当天的）
        for user_id, info in _account_tokens.items():
            if info["date"] == current_date:
                data["accounts"][user_id] = info["tokens"]

        # 只有当有数据时才保存文件
        if data["devices"] or data["accounts"]:
            with open(token_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.bind(tag=TAG).debug(f"Token使用量已保存到文件 {os.path.basename(token_file)}: {len(data['devices'])}个设备, {len(data['accounts'])}个账号")

    except Exception as e:
        logger.bind(tag=TAG).error(f"保存Token使用量到文件失败: {e}")

def _load_token_usage_from_file():
    """从文件加载Token使用量（按日期分文件）"""
    try:
        current_date = datetime.datetime.now().date()
        token_file = _get_token_usage_file(current_date)

        if not os.path.exists(token_file):
            logger.bind(tag=TAG).debug(f"Token使用量文件不存在: {os.path.basename(token_file)}")
            return

        with open(token_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        file_date = datetime.datetime.fromisoformat(data.get("date", "")).date()

        # 只加载当天的数据
        if file_date == current_date:
            # 加载设备Token使用量
            for device_id, tokens in data.get("devices", {}).items():
                if isinstance(tokens, int):
                    _device_tokens[device_id] = {
                        "tokens": tokens,
                        "date": current_date
                    }
                else:
                    # 兼容新格式（带reset_time的）
                    logger.bind(tag=TAG).debug(f"跳过新格式设备数据: {device_id}")

            # 加载账号Token使用量
            for user_id, tokens in data.get("accounts", {}).items():
                if isinstance(tokens, int):
                    _account_tokens[user_id] = {
                        "tokens": tokens,
                        "date": current_date
                    }
                else:
                    # 兼容新格式（带reset_time的）
                    logger.bind(tag=TAG).debug(f"跳过新格式账号数据: {user_id}")

            logger.bind(tag=TAG).info(f"从文件 {os.path.basename(token_file)} 加载Token使用量: {len(_device_tokens)}个设备, {len(_account_tokens)}个账号")
        else:
            logger.bind(tag=TAG).info(f"文件日期不匹配: 文件{file_date} vs 当前{current_date}，跳过加载")

    except Exception as e:
        logger.bind(tag=TAG).error(f"从文件加载Token使用量失败: {e}")

def _cleanup_old_token_files(keep_days: int = 7):
    """清理过期的Token使用量文件

    Args:
        keep_days: 保留天数，默认7天
    """
    try:
        data_dir = _get_data_dir()
        if not os.path.exists(data_dir):
            return

        current_date = datetime.datetime.now().date()
        cutoff_date = current_date - datetime.timedelta(days=keep_days)

        deleted_count = 0
        for filename in os.listdir(data_dir):
            if filename.startswith("token_usage_") and filename.endswith(".json"):
                try:
                    # 从文件名提取日期：token_usage_20250807.json
                    date_str = filename[12:20]  # 提取YYYYMMDD部分
                    file_date = datetime.datetime.strptime(date_str, "%Y%m%d").date()

                    if file_date < cutoff_date:
                        file_path = os.path.join(data_dir, filename)
                        os.remove(file_path)
                        deleted_count += 1
                        logger.bind(tag=TAG).debug(f"删除过期Token文件: {filename}")

                except (ValueError, IndexError) as e:
                    logger.bind(tag=TAG).warning(f"无法解析Token文件日期: {filename}, {e}")

        if deleted_count > 0:
            logger.bind(tag=TAG).info(f"清理完成，删除了 {deleted_count} 个过期Token文件")

    except Exception as e:
        logger.bind(tag=TAG).error(f"清理过期Token文件失败: {e}")

def _check_and_reset_daily_tokens():
    """检查并重置过期的Token计数器（每日0点重置）"""
    current_date = datetime.datetime.now().date()

    # 检查设备Token是否需要重置（日期变化）
    expired_devices = []
    for device_id, info in _device_tokens.items():
        if info["date"] < current_date:
            expired_devices.append(device_id)

    # 重置过期的设备Token
    for device_id in expired_devices:
        old_date = _device_tokens[device_id]["date"]
        logger.bind(tag=TAG).info(f"设备 {device_id} Token计数器日期过期（{old_date} -> {current_date}），重置为0")
        del _device_tokens[device_id]

    # 检查账号Token是否需要重置（日期变化）
    expired_accounts = []
    for user_id, info in _account_tokens.items():
        if info["date"] < current_date:
            expired_accounts.append(user_id)

    # 重置过期的账号Token
    for user_id in expired_accounts:
        old_date = _account_tokens[user_id]["date"]
        logger.bind(tag=TAG).info(f"账号 {user_id} Token计数器日期过期（{old_date} -> {current_date}），重置为0")
        del _account_tokens[user_id]

    # 如果有重置，保存到文件
    if expired_devices or expired_accounts:
        _save_token_usage_to_file()

def reset_token_counters():
    """手动重置所有token计数器（保留兼容性）"""
    global _device_tokens, _account_tokens

    # 保存当前数据到文件
    _save_token_usage_to_file()

    # 清理过期文件
    _cleanup_old_token_files()

    # 清空内存缓存
    _device_tokens.clear()
    _account_tokens.clear()
    logger.bind(tag=TAG).info("Token计数器已手动重置，过期文件已清理")


def get_device_tokens(device_id: str) -> int:
    """获取设备当日的token使用量

    Args:
        device_id: 设备ID（MAC地址）

    Returns:
        int: 当日token使用量
    """
    if not device_id:
        return 0

    _ensure_initialized()
    _check_and_reset_daily_tokens()

    tokens = _device_tokens.get(device_id, {}).get("tokens", 0)
    logger.bind(tag=TAG).debug(f"获取设备token使用量: {device_id} = {tokens}")
    return tokens


def get_account_tokens(user_id: str) -> int:
    """获取账号当日的token使用量

    Args:
        user_id: 用户ID

    Returns:
        int: 当日token使用量
    """
    if not user_id:
        return 0

    _ensure_initialized()
    _check_and_reset_daily_tokens()

    tokens = _account_tokens.get(user_id, {}).get("tokens", 0)
    logger.bind(tag=TAG).debug(f"获取账号token使用量: {user_id} = {tokens}")
    return tokens


def _ensure_initialized():
    """确保Token计数器已初始化"""
    global _initialized
    if not _initialized:
        _load_token_usage_from_file()
        _initialized = True
        logger.bind(tag=TAG).info("Token计数器初始化完成")

def add_device_tokens(device_id: str, token_count: int):
    """增加设备的token使用量（每日0点重置）

    Args:
        device_id: 设备ID（MAC地址）
        token_count: 增加的token数量
    """
    if not device_id or token_count <= 0:
        return

    _ensure_initialized()
    _check_and_reset_daily_tokens()

    current_date = datetime.datetime.now().date()

    # 如果设备不存在，创建新的计数器
    if device_id not in _device_tokens:
        _device_tokens[device_id] = {
            "tokens": 0,
            "date": current_date
        }
        logger.bind(tag=TAG).info(f"设备 {device_id} 开始新的每日Token计数，日期: {current_date}")

    # 增加Token计数
    _device_tokens[device_id]["tokens"] += token_count
    new_count = _device_tokens[device_id]["tokens"]

    # 定期保存到文件（每100个Token保存一次）
    if new_count % 100 == 0:
        _save_token_usage_to_file()

    logger.bind(tag=TAG).debug(f"设备token使用量更新: {device_id} +{token_count} = {new_count}")


def add_account_tokens(user_id: str, token_count: int):
    """增加账号的token使用量（每日0点重置）

    Args:
        user_id: 用户ID
        token_count: 增加的token数量
    """
    if not user_id or token_count <= 0:
        return

    _ensure_initialized()
    _check_and_reset_daily_tokens()

    current_date = datetime.datetime.now().date()

    # 如果账号不存在，创建新的计数器
    if user_id not in _account_tokens:
        _account_tokens[user_id] = {
            "tokens": 0,
            "date": current_date
        }
        logger.bind(tag=TAG).info(f"账号 {user_id} 开始新的每日Token计数，日期: {current_date}")

    # 增加Token计数
    _account_tokens[user_id]["tokens"] += token_count
    new_count = _account_tokens[user_id]["tokens"]

    # 定期保存到文件（每100个Token保存一次）
    if new_count % 100 == 0:
        _save_token_usage_to_file()

    logger.bind(tag=TAG).debug(f"账号token使用量更新: {user_id} +{token_count} = {new_count}")


def check_device_token_limit(device_id: str, max_tokens: int) -> bool:
    """检查设备是否超过token限制
    
    Args:
        device_id: 设备ID（MAC地址）
        max_tokens: 最大token限制
        
    Returns:
        bool: True表示超过限制，False表示未超过
    """
    if not device_id or max_tokens <= 0:
        return False
    
    current_tokens = get_device_tokens(device_id)
    is_exceeded = current_tokens >= max_tokens
    
    # 在判断后添加日志，确保数据准确
    logger.bind(tag=TAG).info(f"设备级Token配额检查: 设备 {device_id}, 当前使用 {current_tokens}, 配额上限 {max_tokens}, 是否超限: {is_exceeded}")

    if is_exceeded:
        logger.bind(tag=TAG).warning(f"设备token配额超限")
    
    return is_exceeded


def check_account_token_limit(user_id: str, max_tokens: int) -> bool:
    """检查账号是否超过token限制
    
    Args:
        user_id: 用户ID
        max_tokens: 最大token限制
        
    Returns:
        bool: True表示超过限制，False表示未超过
    """
    if not user_id or max_tokens <= 0:
        return False
    
    current_tokens = get_account_tokens(user_id)
    is_exceeded = current_tokens >= max_tokens

    # 在判断后添加日志，确保数据准确
    logger.bind(tag=TAG).info(f"账号级Token配额检查: 用户 {user_id}, 当前使用 {current_tokens}, 配额上限 {max_tokens}, 是否超限: {is_exceeded}")
    
    if is_exceeded:
        logger.bind(tag=TAG).warning(f"账号token配额超限")
    
    return is_exceeded


def estimate_tokens_from_text(text: str, model_type: str = "general") -> int:
    """根据文本估算token数量
    
    Args:
        text: 输入文本
        model_type: 模型类型，用于选择估算比例
        
    Returns:
        int: 估算的token数量
    """
    if not text:
        return 0
    
    # 不同模型的估算比例（字符数 -> token数的转换比例）
    # 直接返回文本长度作为token数量
    tokens = len(text)
    logger.bind(tag=TAG).debug(f"Token计算（文本长度）: 文本长度={tokens}, 模型类型={model_type}")
    return tokens


def get_token_usage_stats(device_id: Optional[str] = None, user_id: Optional[str] = None) -> Dict:
    """获取token使用量统计信息
    
    Args:
        device_id: 设备ID（可选）
        user_id: 用户ID（可选）
        
    Returns:
        Dict[str, int]: 统计信息
    """
    stats = {}
    
    if device_id:
        stats["device_tokens"] = get_device_tokens(device_id)
    
    if user_id:
        stats["account_tokens"] = get_account_tokens(user_id)
    
    # 总体统计
    _ensure_initialized()
    _check_and_reset_daily_tokens()

    current_date = datetime.datetime.now().date()
    total_device_tokens = sum(
        info["tokens"] for info in _device_tokens.values()
        if info["date"] == current_date
    )
    total_account_tokens = sum(
        info["tokens"] for info in _account_tokens.values()
        if info["date"] == current_date
    )

    stats.update({
        "total_device_tokens": total_device_tokens,
        "total_account_tokens": total_account_tokens,
        "total_tokens": total_device_tokens + total_account_tokens,
        "active_devices": len([info for info in _device_tokens.values() if info["date"] == current_date]),
        "active_accounts": len([info for info in _account_tokens.values() if info["date"] == current_date]),
        "date": current_date.isoformat()
    })
    
    return stats


# 删除了无用的数据库同步函数 _sync_device_tokens_from_db


# 删除了无用的数据库同步函数 _sync_account_tokens_from_db


def sync_token_usage_from_config(device_id: str, user_id: str, device_tokens: int, account_tokens: int):
    """从配置下发中同步Token使用量（每日重置）

    Args:
        device_id: 设备ID（MAC地址）
        user_id: 用户ID
        device_tokens: 设备当日Token使用量
        account_tokens: 账号当日Token使用量
    """
    _ensure_initialized()
    current_date = datetime.datetime.now().date()

    # 同步设备Token使用量
    if device_id and device_tokens > 0:
        _device_tokens[device_id] = {
            "tokens": device_tokens,
            "date": current_date
        }
        logger.bind(tag=TAG).info(f"从配置同步设备Token使用量: {device_id} = {device_tokens}")

    # 同步账号Token使用量
    if user_id and account_tokens > 0:
        _account_tokens[user_id] = {
            "tokens": account_tokens,
            "date": current_date
        }
        logger.bind(tag=TAG).info(f"从配置同步账号Token使用量: {user_id} = {account_tokens}")

    # 保存到文件
    _save_token_usage_to_file()
