services:
  # 服务器模块 - 开发调试模式
  xiaozhi-esp32-server:
    # build:
    #   context: ../..
    #   dockerfile: Dockerfile-server
    image: xiaozhi-esp32-server:server_latest
    container_name: xiaozhi-esp32-server
    restart: always
    security_opt:
      - seccomp:unconfined
    environment:
      - TZ=Asia/Shanghai
    ports:
      # ws服务端
      - "8100:8000"
      # http服务的端口，用于简单OTA接口(单服务部署)，以及视觉分析接口
      - "8003:8003"
    volumes:
      # 配置文件目录
      - ./data:/opt/xiaozhi-esp32-server/data
      # 模型文件挂接
      - ./models/SenseVoiceSmall/model.pt:/opt/xiaozhi-esp32-server/models/SenseVoiceSmall/model.pt
  # Web模块 - 使用本地构建的镜像（开发版本）
  xiaozhi-esp32-server-web:
    # build:
    #   context: ../..
    #   dockerfile: Dockerfile-web
    image: xiaozhi-esp32-server:web_latest
    container_name: xiaozhi-esp32-server-web
    restart: always
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      # 智控台
      - "8002:8002"
    environment:
      - TZ=Asia/Shanghai
      # 使用本地数据库和Redis配置
      - SPRING_DATASOURCE_DRUID_URL=*****************************************************************************************************************************************************
      - SPRING_DATASOURCE_DRUID_USERNAME=root
      - SPRING_DATASOURCE_DRUID_PASSWORD=Siming@11
      - SPRING_DATA_REDIS_HOST=host.docker.internal
      - SPRING_DATA_REDIS_PORT=6379
    volumes:
      # 配置文件目录
      - ./data/uploadfile:/app/uploadfile