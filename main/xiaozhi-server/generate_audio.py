#!/usr/bin/env python3
"""
生成设备未激活音频文件的脚本
用于更新 mac_device_not_activated.wav 文件
"""

import os
import sys
import subprocess

def generate_audio_file():
    """使用macOS自带的say命令生成WAV音频文件"""

    # 新的提示文本，优化网址读音
    text = "Device not activated. Please visit nous dot matata studio dot com to activate your device."

    # 输出文件路径
    output_file = "config/assets/mac_device_not_activated.wav"

    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        print(f"开始生成音频文件: {text}")

        # 使用macOS的say命令生成WAV文件
        # --data-format=LEI16@24000 表示16位小端整数，24kHz采样率
        # -v Samantha 使用英文女声
        cmd = [
            'say', text,
            '-v', 'Samantha',  # 英文女声
            '-o', output_file,
            '--data-format=LEI16@24000'
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"✅ 音频文件生成成功: {output_file} (大小: {file_size} 字节)")
                return True
            else:
                print("❌ 音频文件生成失败：文件不存在")
                return False
        else:
            print(f"❌ say命令执行失败: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ 生成音频文件时出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = generate_audio_file()
    if success:
        print("🎉 音频文件更新完成！")
        sys.exit(0)
    else:
        print("💥 音频文件生成失败！")
        sys.exit(1)
