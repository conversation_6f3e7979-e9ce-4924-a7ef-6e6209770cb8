from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action

TAG = __name__
logger = setup_logging()

# 定义移动安全检查函数描述
handle_movement_safety_function_desc = {
    "type": "function",
    "function": {
        "name": "handle_movement_safety",
        "description": "移动安全约束检查器。此函数专门用于检查和拒绝超出安全限制的移动请求。\n\n"
                       "⚠️ 重要：此函数不执行任何实际移动！仅用于安全检查和拒绝！\n\n"
                       "🎯 仅在以下情况使用：\n"
                       "- 用户要求转超过2圈（如'转三圈'、'转五圈'、'转1080度'）\n"
                       "- 用户要求极高速度（如'全速'、'最快速度'、'速度100以上'）\n"
                       "- 用户要求长时间移动（如'移动30秒'、'走一分钟'、'持续前进'）\n"
                       "- 用户要求长距离移动（如'走5米'、'前进500厘米'、'移动很远'）\n\n"
                       "🚫 不要用于正常移动请求！正常请求直接使用MCP工具：\n"
                       "- '前进一秒' → self_robot_move_forward\n"
                       "- '左转30度' → self_robot_turn_left\n"
                       "- '右转一圈' → self_robot_turn_right\n"
                       "- '后退' → self_robot_move_backward\n\n"
                       "此函数只用于说'不行'，不用于执行移动！",
        "parameters": {
            "type": "object",
            "properties": {
                "request_type": {
                    "type": "string",
                    "description": "超出限制的请求类型",
                    "enum": ["excessive_rotation", "excessive_speed", "excessive_time", "excessive_distance"]
                },
                "user_request": {
                    "type": "string",
                    "description": "用户的原始请求描述"
                }
            },
            "required": ["request_type", "user_request"]
        }
    }
}


@register_function('handle_movement_safety', handle_movement_safety_function_desc, ToolType.SYSTEM_CTL)
def handle_movement_safety(conn, request_type, user_request):
    """
    移动安全约束检查器。
    
    此函数专门用于检查和拒绝超出安全限制的移动请求。
    不执行任何实际移动，只返回拒绝消息。
    
    :param conn: 连接对象
    :param request_type: 超出限制的请求类型
    :param user_request: 用户的原始请求
    :return: ActionResponse 对象
    """
    logger.bind(tag=TAG).info(f"🚫 [SAFETY_CHECK] 移动安全检查被触发: type={request_type}, request={user_request}")
    
    # 根据不同的超限类型返回相应的拒绝消息
    if request_type == "excessive_rotation":
        response_message = "抱歉，我一次最多只能转两圈（720度），您的要求超出了我的安全限制。"
    elif request_type == "excessive_speed":
        response_message = "抱歉，我的最高速度是100，您要求的速度超出了我的安全限制。"
    elif request_type == "excessive_time":
        response_message = "抱歉，我一次最多只能移动20秒，您要求的时间超出了我的安全限制。"
    elif request_type == "excessive_distance":
        response_message = "抱歉，我一次最多只能移动3米（300厘米），您要求的距离超出了我的安全限制。"
    else:
        response_message = "抱歉，您的移动要求超出了我的安全限制。"
    
    logger.bind(tag=TAG).info(f"📢 [SAFETY_RESPONSE] 返回安全限制消息: {response_message}")
    
    return ActionResponse(
        action=Action.RESPONSE,
        result=response_message,
        response=response_message
    )
