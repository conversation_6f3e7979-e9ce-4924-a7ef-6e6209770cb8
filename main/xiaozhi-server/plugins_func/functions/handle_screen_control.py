from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action

TAG = __name__
logger = setup_logging()

# 定义屏幕控制函数描述，供LLM理解函数的用途
handle_screen_control_function_desc = {
    "type": "function",
    "function": {
        "name": "handle_screen_control",
        "description": "处理屏幕控制相关的询问和请求。\n\n"
                       "**重要说明**：此设备暂时不支持屏幕控制功能！\n\n"
                       "**功能**：\n"
                       "- 当用户询问屏幕控制能力时，告知不支持\n"
                       "- 当用户尝试控制屏幕时，礼貌拒绝并说明原因\n\n"
                       "**适用场景**：\n"
                       "- 用户询问：'你能控制屏幕吗？'、'支持屏幕调节吗？'\n"
                       "- 屏幕控制请求：'调亮屏幕'、'屏幕亮度调到80'、'切换主题'\n"
                       "- 任何涉及屏幕、亮度、主题的控制需求\n\n"
                       "**回复策略**：统一回复不支持屏幕控制功能",
        "parameters": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "请求类型：inquiry(询问能力)、brightness(亮度控制)、theme(主题控制)、other(其他)",
                    "enum": ["inquiry", "brightness", "theme", "other"]
                },
                "value": {
                    "type": "string", 
                    "description": "用户的具体要求，如'调到80'、'调亮一点'、'切换主题'等"
                }
            },
            "required": ["action", "value"]
        }
    }
}


@register_function('handle_screen_control', handle_screen_control_function_desc, ToolType.SYSTEM_CTL)
def handle_screen_control(conn, action, value):
    """
    处理屏幕控制请求的统一入口。
    
    此函数会拦截所有屏幕相关的控制请求，并统一返回不支持的消息。
    这样可以确保用户得到一致的体验，而不是出现工具不存在等技术错误。
    
    :param conn: 连接对象
    :param action: 屏幕控制动作类型
    :param value: 用户的具体要求
    :return: ActionResponse 对象
    """
    logger.bind(tag=TAG).info(f"🚫 [SCREEN_CONTROL_BLOCKED] 屏幕控制请求被拦截: action={action}, value={value}")
    
    # 根据不同的动作类型返回相应的友好消息
    if action == "inquiry":
        response_message = "抱歉，我目前不支持屏幕控制功能，包括亮度调节和主题切换等。"
    elif action == "brightness":
        response_message = "抱歉，我暂时不支持调节屏幕亮度功能。"
    elif action == "theme":
        response_message = "抱歉，我暂时不支持切换屏幕主题功能。"
    else:
        response_message = "抱歉，我暂时不支持屏幕相关的控制功能。"
    
    logger.bind(tag=TAG).info(f"📢 [SCREEN_RESPONSE] 返回屏幕控制不支持消息: {response_message}")
    
    return ActionResponse(
        action=Action.RESPONSE,
        result=response_message,
        response=response_message
    )
