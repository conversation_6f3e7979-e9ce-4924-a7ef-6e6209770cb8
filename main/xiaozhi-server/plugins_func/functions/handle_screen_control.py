from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action

TAG = __name__
logger = setup_logging()

# 定义屏幕控制函数描述，供LLM理解函数的用途
handle_screen_control_function_desc = {
    "type": "function",
    "function": {
        "name": "handle_screen_control",
        "description": "处理所有屏幕相关的控制请求，包括屏幕亮度调节、屏幕主题切换等。\n\n"
                       "**适用场景**：\n"
                       "- 用户要求调节屏幕亮度（如'调亮屏幕'、'屏幕亮度调到80'、'把屏幕调暗'）\n"
                       "- 用户要求切换屏幕主题（如'切换到暗色主题'、'屏幕调成白色'）\n"
                       "- 用户提到屏幕太亮、太暗等屏幕相关问题\n"
                       "- 任何涉及屏幕显示效果的控制需求\n\n"
                       "**重要**：只要用户提到屏幕相关的控制需求，都应该调用此函数！",
        "parameters": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "屏幕控制动作类型",
                    "enum": ["brightness", "theme", "other"]
                },
                "value": {
                    "type": "string", 
                    "description": "用户的具体要求，如'调到80'、'调亮一点'、'切换主题'等"
                }
            },
            "required": ["action", "value"]
        }
    }
}


@register_function('handle_screen_control', handle_screen_control_function_desc, ToolType.SYSTEM_CTL)
def handle_screen_control(conn, action, value):
    """
    处理屏幕控制请求的统一入口。
    
    此函数会拦截所有屏幕相关的控制请求，并统一返回不支持的消息。
    这样可以确保用户得到一致的体验，而不是出现工具不存在等技术错误。
    
    :param conn: 连接对象
    :param action: 屏幕控制动作类型
    :param value: 用户的具体要求
    :return: ActionResponse 对象
    """
    logger.bind(tag=TAG).info(f"🚫 [SCREEN_CONTROL_BLOCKED] 屏幕控制请求被拦截: action={action}, value={value}")
    
    # 根据不同的动作类型返回相应的友好消息
    if action == "brightness":
        response_message = "抱歉，我暂时不支持调节屏幕亮度功能。"
    elif action == "theme":
        response_message = "抱歉，我暂时不支持切换屏幕主题功能。"
    else:
        response_message = "抱歉，我暂时不支持屏幕相关的控制功能。"
    
    logger.bind(tag=TAG).info(f"📢 [SCREEN_RESPONSE] 返回屏幕控制不支持消息: {response_message}")
    
    return ActionResponse(
        action=Action.RESPONSE,
        result=response_message,
        response=None
    )
