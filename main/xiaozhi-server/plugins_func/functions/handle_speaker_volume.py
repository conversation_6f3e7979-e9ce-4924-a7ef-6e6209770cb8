from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action
from core.handle.iotHandle import get_iot_status, send_iot_conn
import asyncio
from typing import Optional

TAG = __name__
logger = setup_logging()


async def _get_speaker_status(conn, property_name):
    """获取音响状态"""
    status = await get_iot_status(conn, "Speaker", property_name)
    if status is None:
        raise Exception(f"你的设备不支持音量控制")
    return status


async def _set_speaker_property(
    conn,
    method_name,
    property_name,
    new_value=None,
    action=None,
    step=10,
):
    """设置音响属性"""
    current_value = await _get_speaker_status(conn, property_name)

    if action == "raise":
        current_value += step
    elif action == "lower":
        current_value -= step
    elif action == "set":
        if new_value is None:
            raise Exception(f"缺少{property_name}参数")
        current_value = new_value

    # 限制音量范围在0到100之间
    current_value = max(0, min(100, current_value))

    await send_iot_conn(conn, "Speaker", method_name, {property_name: current_value})
    return current_value


def _handle_speaker_action(conn, func, success_message, error_message, *args, **kwargs):
    """处理音响操作的通用函数"""
    future = asyncio.run_coroutine_threadsafe(func(conn, *args, **kwargs), conn.loop)
    try:
        result = future.result()
        logger.bind(tag=TAG).info(f"{success_message}: {result}")
        response = f"{success_message}{result}"
        return ActionResponse(action=Action.RESPONSE, result=result, response=response)
    except Exception as e:
        logger.bind(tag=TAG).error(f"{error_message}: {e}")
        response = f"{error_message}: {e}"
        return ActionResponse(action=Action.RESPONSE, result=None, response=response)


# 音量控制函数描述
handle_speaker_volume_function_desc = {
    "type": "function",
    "function": {
        "name": "handle_speaker_volume",
        "description": (
            "专门用于控制设备音量的函数。当用户想要获取、设置、调高或调低音量时调用此函数。\n\n"
            "**适用场景**：\n"
            "- 用户说『声音调小』、『音量调大』\n"
            "- 用户说『声音调到70』、『音量设置为50』\n"
            "- 用户说『调到最大』、『调到最小』\n"
            "- 用户说『现在音量多少』\n\n"
            "**音量控制示例**：\n"
            "- 用户说『声音调小』 → 调用函数：action: lower\n"
            "- 用户说『声音调到70』 → 调用函数：action: set, value: 70\n"
            "- 用户说『调到最大』 → 调用函数：action: set, value: 100\n"
            "- 用户说『调到最小』 → 调用函数：action: set, value: 0\n"
            "- 用户说『音量调大』 → 调用函数：action: raise\n"
            "- 用户说『现在音量多少』 → 调用函数：action: get\n"
        ),
        "parameters": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "动作名称，可选值：get(获取音量),set(设置音量),raise(提高音量),lower(降低音量)",
                    "enum": ["get", "set", "raise", "lower"]
                },
                "value": {
                    "type": "integer",
                    "description": "音量值，范围0-100之间的整数，仅在action为set时使用",
                    "minimum": 0,
                    "maximum": 100
                },
            },
            "required": ["action"],
        },
    },
}


@register_function(
    "handle_speaker_volume",
    handle_speaker_volume_function_desc,
    ToolType.IOT_CTL,
)
def handle_speaker_volume(conn, action: str, value: Optional[int] = None):
    """
    专门处理音响音量控制的函数
    
    :param conn: 连接对象
    :param action: 动作类型 (get/set/raise/lower)
    :param value: 音量值 (0-100)，仅在action为set时使用
    :return: ActionResponse 对象
    """
    logger.bind(tag=TAG).info(f"🔊 [SPEAKER_CONTROL] 音量控制被调用: action={action}, value={value}")
    
    # 检查value是否为中文值，并进行智能转换
    if value is not None and isinstance(value, str):
        value_str = str(value).lower()
        if any("\u4e00" <= char <= "\u9fff" for char in value_str):
            # 处理中文关键词
            if "最大" in value_str or "最高" in value_str or "满" in value_str:
                value = 100
            elif "最小" in value_str or "最低" in value_str or "静音" in value_str:
                value = 0
            elif "一半" in value_str or "中等" in value_str:
                value = 50
            else:
                raise Exception("请直接告诉我要将音量调整成多少")
        else:
            # 处理英文关键词
            if "max" in value_str or "maximum" in value_str:
                value = 100
            elif "min" in value_str or "minimum" in value_str:
                value = 0
            elif "half" in value_str or "middle" in value_str:
                value = 50
            else:
                # 尝试转换为数字
                try:
                    value = int(value)
                except ValueError:
                    raise Exception("请直接告诉我要将音量调整成多少")

    method_name, property_name = "SetVolume", "volume"

    if action not in ["get", "set", "raise", "lower"]:
        raise Exception(f"未识别的动作名称: {action}")

    if action == "get":
        # 获取当前音量
        return _handle_speaker_action(
            conn,
            _get_speaker_status,
            "当前音量",
            "获取音量失败",
            property_name=property_name,
        )
    else:
        # 设置、提高或降低音量
        if action == "set" and value is not None:
            if value == 100:
                success_msg = "已将音量调整到最大"
            elif value == 0:
                success_msg = "已将音量调整到最小"
            else:
                success_msg = f"已将音量调整到{value}%"
        else:
            success_msg = "音量已调整"

        return _handle_speaker_action(
            conn,
            _set_speaker_property,
            success_msg,
            "音量调整失败",
            method_name=method_name,
            property_name=property_name,
            new_value=value,
            action=action,
        )
