def _raise_validation_error(param, value, min_val, max_val):
    """抛出用户友好的错误信息。"""
    if param == "angle":
        circles = max_val // 360
        raise ValueError(f"抱歉，我一次最多只能转{circles}圈（{max_val}度），您要求的{value}度超出了我的能力范围。")
    elif param == "speed":
        raise ValueError(f"抱歉，我的最大速度是{max_val}，您要求的速度{value}超出了我的能力范围。")
    elif param == "time":
        raise ValueError(f"抱歉，我一次最多只能移动{max_val}秒，您要求的时间{value}秒超出了我的能力范围。")
    elif param == "distance":
        raise ValueError(f"抱歉，我一次最多只能移动{max_val}厘米，您要求的距离{value}厘米超出了我的能力范围。")
    else:
        raise ValueError(f"参数{param}的值{value}超出允许范围{min_val}-{max_val}。")

def validate_movement_parameters(action, speed=None, time=None, distance=None, angle=None):
    """
    验证原子移动动作的参数是否在允许的物理范围内。
    如果验证失败，则抛出包含用户友好错误信息的 ValueError。

    :param action: 移动动作 (e.g., 'forward', 'left')
    :param speed: 速度 (0-100)
    :param time: 时间 (0-20s)
    :param distance: 距离 (0-300cm)
    :param angle: 角度 (0-720度)
    """
    # 对 'dance' 或 'stop' 动作，跳过参数验证
    if action in ["dance", "stop"]:
        return

    # 1. 验证速度 (0-100)
    if speed is not None and not (0 <= speed <= 100):
        _raise_validation_error("speed", speed, 0, 100)

    # 2. 验证时间 (0-20s)
    if time is not None and not (0 <= time <= 20):
        _raise_validation_error("time", time, 0, 20)

    # 3. 验证前进/后退的距离 (0-300cm)
    if action in ["forward", "backward"]:
        if distance is not None and not (0 <= distance <= 300):
            _raise_validation_error("distance", distance, 0, 300)

    # 4. 验证左转/右转的角度 (0-720度)
    if action in ["left", "right"]:
        if angle is not None and not (0 <= angle <= 720):
            _raise_validation_error("angle", angle, 0, 720)

    # 5. 确保至少有一个移动参数
    if time is None and distance is None and angle is None:
        raise ValueError("请告诉我具体的移动参数，比如时间、距离或角度。")