#!/usr/bin/env python3
"""
小智ESP32服务器 一键Docker构建工具
最简单的使用方式，直接复用项目配置
"""
import docker
import os
import sys

# 直接使用你的服务器配置
DOCKER_CONFIGS = {
    'test': {
        'host': 'https://**************:2376',
        'cert_path': '/Users/<USER>/.docker/certs/234',
        'name': '测试环境'
    },
    'prod': {
        'host': 'https://*************:2376',
        'cert_path': '/Users/<USER>/.docker/certs',
        'name': '生产环境'
    }
}

def get_client(env='test'):
    """获取Docker客户端"""
    config = DOCKER_CONFIGS[env]
    cert_path = config['cert_path']
    
    # 检查证书文件
    for cert_file in ['ca.pem', 'cert.pem', 'key.pem']:
        full_path = os.path.join(cert_path, cert_file)
        if not os.path.exists(full_path):
            print(f"❌ 证书文件不存在: {full_path}")
            return None
    
    # 创建客户端
    tls_config = docker.tls.TLSConfig(
        client_cert=(f"{cert_path}/cert.pem", f"{cert_path}/key.pem"),
        ca_cert=f"{cert_path}/ca.pem",
        verify=True
    )
    
    return docker.DockerClient(base_url=config['host'], tls=tls_config)

def quick_build(env='test', image_type='server', tag='latest'):
    """一键构建并推送"""
    config = DOCKER_CONFIGS[env]
    dockerfile = f'Dockerfile-{image_type}'
    image_name = f'ghcr.nju.edu.cn/xinnan-tech/xiaozhi-esp32-server:{image_type}_{tag}'
    
    print(f"🚀 一键构建并推送")
    print(f"   🌍 环境: {config['name']}")
    print(f"   📦 类型: {image_type}")
    print(f"   🏷️  标签: {tag}")
    print(f"   📄 Dockerfile: {dockerfile}")
    print(f"   🏷️  镜像名: {image_name}")
    print("=" * 60)
    
    client = get_client(env)
    if not client:
        return False
    
    try:
        # 构建镜像
        print("🔨 开始构建...")
        image, logs = client.images.build(
            path='.',
            dockerfile=dockerfile,
            tag=image_name,
            rm=True,
            pull=True
        )
        
        # 显示关键构建信息
        for log in logs:
            if 'stream' in log:
                line = log['stream'].strip()
                if any(keyword in line.lower() for keyword in ['step', 'successfully', 'error', 'failed']):
                    print(f"   {line}")
        
        print(f"✅ 构建完成: {image.short_id}")
        
        # 推送镜像
        print("📤 开始推送...")
        for line in client.images.push(image_name, stream=True, decode=True):
            if 'status' in line and 'progress' not in line:
                print(f"   📊 {line['status']}")
        
        print(f"✅ 推送完成: {image_name}")
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False

def list_images(env='test'):
    """列出镜像"""
    config = DOCKER_CONFIGS[env]
    client = get_client(env)
    if not client:
        return
    
    print(f"📋 {config['name']}镜像列表:")
    print("-" * 80)
    
    try:
        images = client.images.list()
        for image in images:
            tags = ', '.join(image.tags) if image.tags else '<none>'
            print(f"🆔 {image.short_id} | 🏷️  {tags}")
    except Exception as e:
        print(f"❌ 获取失败: {e}")

def main():
    """命令行入口"""
    if len(sys.argv) < 2:
        print("🔧 小智ESP32 一键Docker工具")
        print("=" * 40)
        print("使用方法:")
        print("  python quick_docker.py build <env> [type] [tag]")
        print("  python quick_docker.py list <env>")
        print("")
        print("快捷示例:")
        print("  python quick_docker.py build test")
        print("  python quick_docker.py build test server v1.0")
        print("  python quick_docker.py build prod web latest")
        print("  python quick_docker.py list test")
        return
    
    action = sys.argv[1]
    
    if action == 'build':
        env = sys.argv[2] if len(sys.argv) > 2 else 'test'
        image_type = sys.argv[3] if len(sys.argv) > 3 else 'server'
        tag = sys.argv[4] if len(sys.argv) > 4 else 'latest'
        
        if env not in DOCKER_CONFIGS:
            print(f"❌ 错误: 未知环境 '{env}', 请使用 test 或 prod")
            return
        
        quick_build(env, image_type, tag)
        
    elif action == 'list':
        env = sys.argv[2] if len(sys.argv) > 2 else 'test'
        
        if env not in DOCKER_CONFIGS:
            print(f"❌ 错误: 未知环境 '{env}', 请使用 test 或 prod")
            return
        
        list_images(env)
        
    else:
        print(f"❌ 未知操作: {action}")

if __name__ == '__main__':
    main()
