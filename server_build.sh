#!/bin/bash

# server_build.sh
# 该脚本在服务器上执行，用于解压源代码、构建Docker镜像并进行清理。

# 在任何命令失败时立即退出
set -e

# --- 配置 ---
# --- 日志函数 ---
log_info() {
    echo "[INFO] $1"
}

log_error() {
    echo "[ERROR] $1" >&2
}

# --- 脚本开始 ---
log_info "启动服务器端构建流程..."

# 1. 检查参数
if [ -z "$1" ]; then
    log_error "缺少参数：需要提供源代码压缩包的文件名。"
    log_error "用法: $0 <source_archive.tar.gz>"
    exit 1
fi

SOURCE_ARCHIVE=$1
# 从文件名中提取目录名 (例如, 'source.tar.gz' -> 'source')
# 从文件名中提取构建目录名 (例如, 'source.tar.gz' -> 'source-build')
BUILD_DIR=$(basename "$SOURCE_ARCHIVE" .tar.gz)

if [ ! -f "$SOURCE_ARCHIVE" ]; then
    log_error "源代码压缩包未找到: $SOURCE_ARCHIVE"
    exit 1
fi

# 2. 创建并进入构建目录
log_info "创建并进入构建目录: $BUILD_DIR"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 3. 解压源代码
log_info "正在解压源代码从 ../$SOURCE_ARCHIVE..."
tar -xzf ../"$SOURCE_ARCHIVE"
if [ $? -ne 0 ]; then
    log_error "解压失败: ../$SOURCE_ARCHIVE"
    cd ..
    rm -rf "$BUILD_DIR"
    exit 1
fi
log_info "解压完成。"

# 4. 构建 Docker 镜像
# 构建 web 镜像
log_info "开始构建 web 镜像: xiaozhi-esp32-server:web_latest..."
docker build -f Dockerfile-web -t xiaozhi-esp32-server:web_latest .
if [ $? -ne 0 ]; then
    log_error "Web 镜像构建失败。"
    cd ..
    exit 1
fi
log_info "Web 镜像构建成功: xiaozhi-esp32-server:web_latest"

# 构建 server 镜像
log_info "开始构建 server 镜像: xiaozhi-esp32-server:server_latest..."
docker build -f Dockerfile-server -t xiaozhi-esp32-server:server_latest .
if [ $? -ne 0 ]; then
    log_error "Server 镜像构建失败。"
    cd ..
    exit 1
fi
log_info "Server 镜像构建成功: xiaozhi-esp32-server:server_latest"

# 5. 返回上级目录
log_info "返回上级目录..."
cd ..

# 6. 清理
log_info "开始清理工作区..."
log_info "删除上传的压缩包: $SOURCE_ARCHIVE"
rm -f "$SOURCE_ARCHIVE"

log_info "删除构建目录: $BUILD_DIR"
rm -rf "$BUILD_DIR"

log_info "清理完成。"
log_info "服务器端构建流程成功结束！"

exit 0