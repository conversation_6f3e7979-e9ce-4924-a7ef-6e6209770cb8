#!/usr/bin/env python3
"""
测试Matatalab账号体系集成
验证xiaozhi系统是否成功使用matatalab的mt_user表和JWT token
"""

import requests
import json
import time
import tempfile
import webbrowser
import os
from typing import Dict, Any, Optional

class MatatalabIntegrationTester:
    def __init__(self, base_url: str = "http://localhost:8002/xiaozhi"):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = None
        
    def get_captcha_with_display(self) -> tuple[Optional[str], Optional[str]]:
        """获取验证码并显示图片，返回(captcha_id, user_input_code)"""
        import uuid
        captcha_id = str(uuid.uuid4())

        url = f"{self.base_url}/user/captcha"
        params = {"uuid": captcha_id}

        try:
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                print(f"✅ 验证码获取成功，ID: {captcha_id}")

                # 保存验证码图片到临时文件
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                    tmp_file.write(response.content)
                    tmp_file_path = tmp_file.name

                print(f"验证码图片已保存到: {tmp_file_path}")

                # 尝试在浏览器中打开验证码图片
                try:
                    webbrowser.open(f'file://{tmp_file_path}')
                    print("验证码图片已在浏览器中打开")
                except:
                    print("无法自动打开浏览器，请手动打开验证码图片")

                # 等待用户输入验证码
                user_code = input("请查看验证码图片并输入验证码: ").strip()

                # 清理临时文件
                try:
                    os.unlink(tmp_file_path)
                except:
                    pass

                return captcha_id, user_code
            else:
                print(f"❌ 验证码获取失败，状态码: {response.status_code}")
                return None, None
        except Exception as e:
            print(f"❌ 验证码获取异常: {e}")
            return None, None

    def get_captcha(self) -> Optional[str]:
        """获取验证码（兼容旧方法）"""
        import uuid
        captcha_id = str(uuid.uuid4())
        url = f"{self.base_url}/user/captcha"
        params = {"uuid": captcha_id}

        try:
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                print(f"✅ 验证码获取成功，ID: {captcha_id}")
                return captcha_id
            else:
                print(f"❌ 验证码获取失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 验证码获取异常: {e}")
            return None

    def test_user_register(self, username: str, password: str, email: str, mobile: str, interactive: bool = True) -> bool:
        """测试用户注册"""
        print(f"\n=== 测试用户注册: {username} ===")

        if interactive:
            # 交互式注册：显示验证码图片并等待用户输入
            print("使用交互式验证码注册...")
            captcha_id, user_code = self.get_captcha_with_display()

            if not captcha_id or not user_code:
                print("❌ 无法获取验证码或用户未输入验证码")
                return False

            url = f"{self.base_url}/user/register"
            data = {
                "username": username,
                "password": password,
                "email": email,
                "mobile": mobile,
                "captcha": user_code,
                "captchaId": captcha_id
            }

            try:
                response = self.session.post(url, json=data)
                print(f"注册请求状态码: {response.status_code}")
                print(f"注册响应: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 0:
                        print(f"✅ 注册成功")
                        return True
                    else:
                        print(f"❌ 注册失败: {result.get('msg')}")
                        return False
                else:
                    print(f"❌ 注册请求失败，状态码: {response.status_code}")
                    return False

            except Exception as e:
                print(f"❌ 注册异常: {e}")
                return False
        else:
            print("❌ 注册功能需要交互模式")
            return False

    def test_user_login(self, username: str, password: str, interactive: bool = True) -> bool:
        """测试用户登录"""
        print(f"\n=== 测试用户登录: {username} ===")

        if interactive:
            # 交互式登录：显示验证码图片并等待用户输入
            print("使用交互式验证码登录...")
            captcha_id, user_code = self.get_captcha_with_display()

            if not captcha_id or not user_code:
                print("❌ 无法获取验证码或用户未输入验证码")
                return False

            url = f"{self.base_url}/user/login"
            data = {
                "username": username,
                "password": password,
                "captcha": user_code,
                "captchaId": captcha_id
            }

            try:
                response = self.session.post(url, json=data)
                print(f"登录请求状态码: {response.status_code}")
                print(f"登录响应: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 0:
                        self.token = result.get("data", {}).get("token")
                        print(f"✅ 登录成功，获取到token: {self.token[:20]}...")
                        return True
                    else:
                        print(f"❌ 登录失败: {result.get('msg')}")
                        return False
                else:
                    print(f"❌ 登录请求失败，状态码: {response.status_code}")
                    return False

            except Exception as e:
                print(f"❌ 登录异常: {e}")
                return False
        else:
            # 自动化登录：尝试多种方式
            print("使用自动化方式尝试登录...")
            login_attempts = [
                # 尝试1: 不使用验证码
                {"username": username, "password": password},
                # 尝试2: 使用空验证码
                {"username": username, "password": password, "captcha": "", "captchaId": ""},
                # 尝试3: 使用固定验证码
                {"username": username, "password": password, "captcha": "12345", "captchaId": "test"},
            ]

            # 尝试4: 获取真实验证码ID并使用常见验证码
            captcha_id = self.get_captcha()
            if captcha_id:
                common_codes = ["12345", "123456", "00000", "11111", "test", "admin"]
                for code in common_codes:
                    login_attempts.append({
                        "username": username,
                        "password": password,
                        "captcha": code,
                        "captchaId": captcha_id
                    })

            url = f"{self.base_url}/user/login"

            for i, data in enumerate(login_attempts, 1):
                try:
                    print(f"尝试登录方式 {i}: {data.get('captcha', '无验证码')}")
                    response = self.session.post(url, json=data)
                    print(f"登录请求状态码: {response.status_code}")

                    if response.status_code == 200:
                        result = response.json()
                        if result.get("code") == 0:
                            self.token = result.get("data", {}).get("token")
                            print(f"✅ 登录成功，获取到token: {self.token[:20]}...")
                            return True
                        else:
                            print(f"登录失败: {result.get('msg')}")
                    else:
                        print(f"请求失败，状态码: {response.status_code}")

                except Exception as e:
                    print(f"登录异常: {e}")

            print("❌ 所有自动登录尝试都失败了")
            return False
    
    def test_user_info(self) -> bool:
        """测试获取用户信息"""
        print(f"\n=== 测试获取用户信息 ===")
        
        if not self.token:
            print("❌ 没有token，请先登录")
            return False
            
        url = f"{self.base_url}/user/info"
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            response = self.session.get(url, headers=headers)
            print(f"用户信息请求状态码: {response.status_code}")
            print(f"用户信息响应: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    user_info = result.get("data", {})
                    print(f"✅ 获取用户信息成功:")
                    print(f"   用户ID: {user_info.get('id')}")
                    print(f"   用户名: {user_info.get('username')}")
                    print(f"   超级管理员: {user_info.get('superAdmin')}")
                    return True
                else:
                    print(f"❌ 获取用户信息失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 用户信息请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取用户信息异常: {e}")
            return False
    
    def get_server_secret(self) -> str:
        """获取服务器密钥"""
        # 首先尝试从公共配置接口获取（这个接口不需要认证）
        url = f"{self.base_url}/user/pub-config"
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    print("✅ 从公共配置获取信息成功")
                    # 公共配置中没有server secret，我们需要使用已知的默认值
                    # 或者从数据库中查询得到的值
                    return "8267ddcc-8f7c-454b-9713-4c3925dbc650"
        except Exception as e:
            print(f"⚠️ 获取公共配置失败: {e}")

        # 返回默认的server secret（这个值应该从系统参数中获取）
        return "8267ddcc-8f7c-454b-9713-4c3925dbc650"

    def test_system_config(self) -> bool:
        """测试系统配置接口 - 使用Server-Secret认证"""
        print(f"\n=== 测试系统配置接口 ===")

        # 获取server secret
        server_secret = self.get_server_secret()
        print(f"使用Server-Secret: {server_secret[:10]}...")

        url = f"{self.base_url}/config/server-base"
        headers = {
            "Authorization": f"Bearer {server_secret}",
            "Content-Type": "application/json"
        }

        try:
            # 注意：这个接口是POST方法，不是GET
            response = self.session.post(url, headers=headers)
            print(f"系统配置请求状态码: {response.status_code}")
            print(f"系统配置响应: {response.text[:200]}...")

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    config = result.get("data", {})
                    print(f"✅ 获取系统配置成功，配置项数量: {len(config)}")
                    # 显示部分配置
                    config_keys = list(config.keys())[:5] if isinstance(config, dict) else []
                    for key in config_keys:
                        value = config[key]
                        # 如果值太长，截断显示
                        if isinstance(value, str) and len(value) > 50:
                            value = value[:50] + "..."
                        print(f"   {key}: {value}")
                    return True
                else:
                    print(f"❌ 获取系统配置失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 系统配置请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 获取系统配置异常: {e}")
            return False
    
    def test_agent_list(self) -> bool:
        """测试智能体列表接口"""
        print(f"\n=== 测试智能体列表接口 ===")

        if not self.token:
            print("❌ 没有token，请先登录")
            return False

        url = f"{self.base_url}/agent/list"
        headers = {"Authorization": f"Bearer {self.token}"}

        try:
            # 注意：这个接口不需要分页参数，直接返回用户的智能体列表
            response = self.session.get(url, headers=headers)
            print(f"智能体列表请求状态码: {response.status_code}")
            print(f"智能体列表响应: {response.text[:200]}...")

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    agents = result.get("data", [])  # 直接是数组，不是分页对象
                    print(f"✅ 获取智能体列表成功，总数: {len(agents)}")
                    for agent in agents[:3]:  # 显示前3个
                        print(f"   智能体: {agent.get('agentName')} (ID: {agent.get('id')})")
                    return True
                else:
                    print(f"❌ 获取智能体列表失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 智能体列表请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 获取智能体列表异常: {e}")
            return False

    def test_other_token_apis(self) -> bool:
        """测试其他需要token认证的API接口"""
        print(f"\n=== 测试其他Token认证API ===")

        if not self.token:
            print("❌ 没有token，请先登录")
            return False

        headers = {"Authorization": f"Bearer {self.token}"}
        success_count = 0
        total_count = 0

        # 测试接口列表
        test_apis = [
            {
                "name": "获取智能体模板",
                "method": "GET",
                "url": f"{self.base_url}/agent/template",
                "params": None
            },
            {
                "name": "获取模型名称列表",
                "method": "GET",
                "url": f"{self.base_url}/models/names",
                "params": {"modelType": "LLM"}
            },
            {
                "name": "获取绑定设备列表",
                "method": "GET",
                "url": f"{self.base_url}/device/bind/OFFICIAL_DEFAULT_AGENT",
                "params": None
            },
            {
                "name": "获取音色分页列表",
                "method": "GET",
                "url": f"{self.base_url}/ttsVoice",
                "params": {"ttsModelId": "TTS_EdgeTTS", "page": 1, "limit": 10}
            },
            {
                "name": "获取模型供应器列表",
                "method": "GET",
                "url": f"{self.base_url}/models/provider",
                "params": {"page": 1, "limit": 10}
            },
            {
                "name": "获取模型配置列表(需要超级管理员权限)",
                "method": "GET",
                "url": f"{self.base_url}/models/list",
                "params": {"modelType": "LLM", "page": 1, "limit": 10}
            },
            {
                "name": "获取用户管理列表",
                "method": "GET",
                "url": f"{self.base_url}/admin/users",
                "params": {"page": 1, "limit": 10}
            }
        ]

        for api in test_apis:
            total_count += 1
            try:
                print(f"\n--- 测试: {api['name']} ---")

                if api["method"] == "GET":
                    response = self.session.get(api["url"], headers=headers, params=api["params"])
                elif api["method"] == "POST":
                    response = self.session.post(api["url"], headers=headers, json=api["params"])

                print(f"请求状态码: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 0:
                        data = result.get("data")
                        if isinstance(data, list):
                            print(f"✅ {api['name']} 成功，返回 {len(data)} 条记录")
                        elif isinstance(data, dict):
                            print(f"✅ {api['name']} 成功，返回数据: {len(data)} 个字段")
                        else:
                            print(f"✅ {api['name']} 成功")
                        success_count += 1
                    else:
                        print(f"❌ {api['name']} 失败: {result.get('msg')}")
                elif response.status_code == 403:
                    print(f"⚠️ {api['name']} 权限不足")
                elif response.status_code == 404:
                    print(f"⚠️ {api['name']} 接口不存在")
                else:
                    print(f"❌ {api['name']} 请求失败，状态码: {response.status_code}")

            except Exception as e:
                print(f"❌ {api['name']} 异常: {e}")

        print(f"\n📊 其他API测试结果: {success_count}/{total_count} 成功")
        return success_count > 0  # 至少有一个成功就算通过

    def test_login_with_credentials(self, login_name: str, password: str, login_type: str = "登录") -> bool:
        """使用指定凭据测试登录"""
        print(f"尝试{login_type}...")

        # 获取验证码
        captcha_result = self.get_captcha_with_display()
        if not captcha_result or captcha_result[0] is None:
            print(f"❌ 获取验证码失败")
            return False

        captcha_id, captcha_text = captcha_result

        # 构建登录请求
        login_data = {
            "username": login_name,
            "password": password,
            "captcha": captcha_text,
            "captchaId": captcha_id
        }

        try:
            response = self.session.post(f"{self.base_url}/user/login", json=login_data)
            print(f"{login_type}请求状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    token_data = result.get("data", {})
                    self.token = token_data.get("token")
                    print(f"✅ {login_type}成功")
                    print(f"   Token: {self.token[:50]}...")
                    return True
                else:
                    print(f"❌ {login_type}失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ {login_type}请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ {login_type}异常: {e}")
            return False

    def get_current_user_info(self):
        """获取当前用户信息"""
        if not self.token:
            return None

        try:
            headers = {"Authorization": f"Bearer {self.token}"}
            response = self.session.get(f"{self.base_url}/user/info", headers=headers)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    return result.get("data", {})
            return None
        except Exception:
            return None

    def test_admin_users(self) -> bool:
        """测试用户管理接口"""
        print(f"\n=== 测试用户管理接口 ===")

        if not self.token:
            print("❌ 没有token，请先登录")
            return False

        url = f"{self.base_url}/admin/users"
        headers = {"Authorization": f"Bearer {self.token}"}
        params = {"page": 1, "limit": 10}

        try:
            response = self.session.get(url, headers=headers, params=params)
            print(f"用户管理请求状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    data = result.get("data", {})
                    users = data.get("list", [])
                    total = data.get("total", 0)
                    print(f"✅ 获取用户管理列表成功，总数: {total}")
                    for user in users[:3]:  # 显示前3个
                        print(f"   用户: {user.get('mobile')} (ID: {user.get('userid')})")
                    return True
                else:
                    print(f"❌ 获取用户管理列表失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 用户管理请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 获取用户管理列表异常: {e}")
            return False

    def run_all_tests(self, username: str, password: str, email: str, mobile: str, interactive: bool = True) -> bool:
        """运行所有测试"""
        print("🚀 开始测试Matatalab账号体系集成...")

        # 先尝试注册用户
        print("尝试注册新用户...")
        if self.test_user_register(username, password, email, mobile, interactive):
            print("✅ 用户注册成功")
        else:
            print("⚠️ 用户注册失败，可能用户已存在，继续测试登录...")

        # 测试登录
        if not self.test_user_login(username, password, interactive):
            print("\n❌ 登录测试失败，停止后续测试")
            return False
        
        # 测试用户信息
        user_info_success = self.test_user_info()
        
        # 测试系统配置
        config_success = self.test_system_config()
        
        # 测试智能体列表
        agent_success = self.test_agent_list()
        
        # 总结
        print(f"\n📊 测试结果总结:")
        print(f"   登录测试: ✅ 成功")
        print(f"   用户信息: {'✅ 成功' if user_info_success else '❌ 失败'}")
        print(f"   系统配置: {'✅ 成功' if config_success else '❌ 失败'}")
        print(f"   智能体列表: {'✅ 成功' if agent_success else '❌ 失败'}")
        
        all_success = user_info_success and config_success and agent_success
        
        if all_success:
            print("\n🎉 所有测试通过！Matatalab账号体系集成成功！")
        else:
            print("\n⚠️  部分测试失败，请检查系统配置")
            
        return all_success

def test_multiple_login_methods():
    """测试多种登录方式"""
    tester = MatatalabIntegrationTester()

    print("🚀 开始测试多种登录方式...")

    # 测试账号信息
    mobile = "18038035817"
    password = "Qwer1234"

    login_results = []

    # 1. 测试手机号登录
    print(f"\n=== 测试手机号登录 ===")
    print(f"手机号: {mobile}")
    print(f"密码: {password}")

    mobile_login_success = tester.test_login_with_credentials(mobile, password, "手机号登录")
    login_results.append(("手机号登录", mobile_login_success))

    if mobile_login_success:
        # 测试用户信息和其他API
        user_info_success = tester.test_user_info()
        agent_success = tester.test_agent_list()
        admin_users_success = tester.test_admin_users()

        print(f"\n📊 手机号登录后的API测试:")
        print(f"   用户信息: {'✅ 成功' if user_info_success else '❌ 失败'}")
        print(f"   智能体列表: {'✅ 成功' if agent_success else '❌ 失败'}")
        print(f"   用户管理: {'✅ 成功' if admin_users_success else '❌ 失败'}")

        # 2. 尝试查找该用户的用户名，然后测试用户名登录
        user_info = tester.get_current_user_info()
        if user_info:
            username = user_info.get('username')
            # 如果用户名不同于手机号，测试用户名登录
            if username and username != mobile:
                print(f"\n=== 测试用户名登录 ===")
                print(f"用户名: {username}")
                username_login_success = tester.test_login_with_credentials(username, password, "用户名登录")
                login_results.append(("用户名登录", username_login_success))

    # 3. 测试系统配置接口（使用Server-Secret）
    config_success = tester.test_system_config()

    print(f"\n📊 多种登录方式测试结果总结:")
    for method, success in login_results:
        print(f"   {method}: {'✅ 成功' if success else '❌ 失败'}")
    print(f"   系统配置: {'✅ 成功' if config_success else '❌ 失败'}")

    return len([r for r in login_results if r[1]]) > 0 and config_success

def test_with_known_token():
    """使用已知token进行测试（跳过登录步骤）"""
    tester = MatatalabIntegrationTester()

    print("🚀 开始测试Matatalab账号体系集成（跳过登录）...")

    # 测试系统配置接口（使用Server-Secret）
    config_success = tester.test_system_config()

    # 尝试使用一个已知的有效token（从之前的成功登录中获取）
    # 这个token是从之前的测试中获取的，可能已过期
    known_token = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxNTQ3NyIsInVzZXJuYW1lIjoiYWRtaW4iLCJpYXQiOjE3NDk3ODkwNTAsImV4cCI6MTc1MDM5Mzg1MH0.dYwiKzn2KthycKWuYdqcDSmARlQTH_dP1A7ZnvYW0TM"

    print(f"\n尝试使用已知token测试其他接口...")
    tester.token = known_token

    # 测试用户信息接口
    user_info_success = tester.test_user_info()

    # 测试智能体列表接口
    agent_success = tester.test_agent_list()

    # 测试其他需要token的接口
    other_apis_success = tester.test_other_token_apis()

    print(f"\n📊 测试结果总结:")
    print(f"   系统配置: {'✅ 成功' if config_success else '❌ 失败'}")
    print(f"   用户信息: {'✅ 成功' if user_info_success else '❌ 失败'}")
    print(f"   智能体列表: {'✅ 成功' if agent_success else '❌ 失败'}")
    print(f"   其他API: {'✅ 成功' if other_apis_success else '❌ 失败'}")

    return config_success and user_info_success and agent_success and other_apis_success

def main():
    """主函数"""
    import sys

    tester = MatatalabIntegrationTester()

    # 使用测试账号
    username = "admin"  # 测试用户名
    password = "123456"  # 测试密码（对应数据库中的MD5+盐哈希）
    email = "<EMAIL>"  # 测试邮箱
    mobile = "13800138000"  # 测试手机号

    # 检查命令行参数
    interactive = True
    test_mode = "full"

    if len(sys.argv) > 1:
        if sys.argv[1] == "--auto":
            interactive = False
            print("使用自动化模式（尝试绕过验证码）")
        elif sys.argv[1] == "--config-only":
            test_mode = "config"
            print("仅测试系统配置接口（跳过登录）")
        elif sys.argv[1] == "--multi-login":
            test_mode = "multi-login"
            print("测试多种登录方式（手机号、用户名、邮箱）")
        else:
            print("使用交互模式（显示验证码图片）")
            print("可用参数: --auto (自动化模式), --config-only (仅测试配置), --multi-login (多种登录方式)")
    else:
        print("使用交互模式（显示验证码图片）")
        print("可用参数: --auto (自动化模式), --config-only (仅测试配置), --multi-login (多种登录方式)")

    print("=" * 60)
    print("Matatalab账号体系集成测试")
    print("=" * 60)
    print(f"测试目标: {tester.base_url}")
    print(f"测试账号: {username}")
    print(f"测试邮箱: {email}")
    print(f"测试手机: {mobile}")
    print(f"测试模式: {'交互式' if interactive else '自动化'}")

    if test_mode == "config":
        success = test_with_known_token()
    elif test_mode == "multi-login":
        success = test_multiple_login_methods()
    else:
        success = tester.run_all_tests(username, password, email, mobile, interactive)

    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
